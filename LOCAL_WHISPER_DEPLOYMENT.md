# 局域网Whisper服务部署指南

## 概述

本指南将帮助您在局域网内部署Whisper语音识别服务，实现高准确度的视频字幕生成，同时保证数据隐私。

## 系统要求

### 服务器端（运行Whisper服务的设备）

**最低要求:**
- CPU: 4核心以上
- 内存: 8GB RAM（推荐16GB）
- 存储: 10GB可用空间
- 操作系统: Linux/macOS/Windows
- Python: 3.8+

**推荐配置:**
- CPU: 8核心以上
- 内存: 16GB+ RAM
- GPU: NVIDIA GPU（可选，显著提升性能）
- 存储: SSD硬盘

**GPU支持（可选但推荐）:**
- NVIDIA GPU with CUDA support
- 显存: 4GB+（base模型），8GB+（large模型）

### 客户端（前端应用）
- 现代浏览器（Chrome/Edge/Firefox）
- 与服务器在同一局域网

## 快速部署

### 方法1: 使用启动脚本（推荐）

1. **下载服务端代码**
```bash
# 创建项目目录
mkdir whisper-server
cd whisper-server

# 下载文件（或从项目中复制）
# app.py, requirements.txt, config.py, start.sh
```

2. **安装依赖**
```bash
chmod +x start.sh
./start.sh --install-deps
```

3. **启动服务**
```bash
# 使用默认配置启动
./start.sh

# 或指定模型大小
WHISPER_MODEL_SIZE=small ./start.sh

# 或指定端口
WHISPER_PORT=8080 ./start.sh
```

### 方法2: 手动安装

1. **安装Python依赖**
```bash
pip install -r requirements.txt
```

2. **启动服务**
```bash
python app.py
```

### 方法3: 使用Docker

1. **构建镜像**
```bash
docker build -t whisper-server .
```

2. **运行容器**
```bash
# CPU版本
docker run -p 8000:8000 whisper-server

# GPU版本（需要nvidia-docker）
docker run --gpus all -p 8000:8000 whisper-server
```

## 详细配置

### 环境变量配置

```bash
# 模型配置
export WHISPER_MODEL_SIZE=base          # tiny|base|small|medium|large
export WHISPER_USE_GPU=auto             # true|false|auto

# 服务配置
export WHISPER_HOST=0.0.0.0             # 监听地址
export WHISPER_PORT=8000                # 监听端口

# 性能配置
export WHISPER_MAX_FILE_SIZE=104857600  # 最大文件大小(100MB)
export WHISPER_MAX_DURATION=600         # 最大音频长度(秒)
export WHISPER_MAX_WORKERS=4            # 最大工作进程数

# 安全配置
export WHISPER_ALLOWED_ORIGINS=*        # 允许的来源
export WHISPER_API_KEY=                 # API密钥(可选)
```

### 模型选择指南

| 模型 | 大小 | 内存需求 | 速度 | 准确度 | 适用场景 |
|------|------|----------|------|--------|----------|
| tiny | 39MB | ~1GB | 最快 | 较低 | 快速测试 |
| base | 74MB | ~2GB | 快 | 良好 | 一般使用 |
| small | 244MB | ~3GB | 中等 | 很好 | 推荐配置 |
| medium | 769MB | ~6GB | 慢 | 优秀 | 高质量需求 |
| large | 1550MB | ~12GB | 最慢 | 最佳 | 专业用途 |

## 网络配置

### 防火墙设置

**Linux (ufw):**
```bash
sudo ufw allow 8000/tcp
```

**Linux (iptables):**
```bash
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

**Windows:**
```powershell
netsh advfirewall firewall add rule name="Whisper Server" dir=in action=allow protocol=TCP localport=8000
```

### 端口转发（如需要）

如果服务器在NAT后面，可能需要配置端口转发：
```bash
# 路由器配置示例
外部端口: 8000 -> 内部IP:8000
```

## 客户端配置

### 自动发现

前端应用会自动搜索局域网内的Whisper服务：

1. 打开前端应用
2. 在控制面板中选择"局域网识别"模式
3. 点击"配置"按钮
4. 点击"自动发现服务"

### 手动配置

如果自动发现失败，可以手动配置：

1. 获取服务器IP地址：
```bash
# Linux/macOS
ip addr show | grep inet
# 或
ifconfig | grep inet

# Windows
ipconfig
```

2. 在前端应用中输入服务器地址：
```
*************:8000
```

## 测试验证

### 1. 健康检查

```bash
curl http://服务器IP:8000/health
```

预期响应：
```json
{
  "status": "healthy",
  "model_loaded": true,
  "model_size": "base",
  "device": "cpu"
}
```

### 2. 服务信息

```bash
curl http://服务器IP:8000/info
```

### 3. 语音识别测试

```bash
curl -X POST \
  -F "audio=@test.wav" \
  -F "language=zh" \
  http://服务器IP:8000/transcribe
```

## 性能优化

### GPU加速

1. **安装CUDA**（NVIDIA GPU）
```bash
# 检查GPU
nvidia-smi

# 安装PyTorch GPU版本
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu128
```

2. **验证GPU使用**
```bash
python -c "import torch; print(torch.cuda.is_available())"
```

### 内存优化

```bash
# 限制模型缓存
export WHISPER_CACHE_DIR=/tmp/whisper

# 使用较小模型
export WHISPER_MODEL_SIZE=base
```

### 并发处理

```bash
# 增加工作进程
export WHISPER_MAX_WORKERS=8

# 使用Gunicorn（生产环境）
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查服务是否启动
   - 检查防火墙设置
   - 确认端口号正确

2. **模型加载失败**
   - 检查网络连接（首次下载模型）
   - 检查磁盘空间
   - 检查内存是否足够

3. **识别速度慢**
   - 考虑使用GPU
   - 使用较小的模型
   - 减少音频文件大小

4. **内存不足**
   - 使用较小模型
   - 增加系统内存
   - 限制并发请求

### 日志查看

```bash
# 查看服务日志
tail -f /var/log/whisper.log

# 或在Docker中
docker logs whisper-server
```

### 性能监控

```bash
# 监控系统资源
htop

# 监控GPU使用
nvidia-smi -l 1

# 监控网络
netstat -an | grep 8000
```

## 安全考虑

### 1. 网络安全

- 仅在可信局域网内使用
- 考虑使用VPN访问
- 定期更新系统和依赖

### 2. 访问控制

```bash
# 启用API密钥
export WHISPER_API_KEY=your_secret_key

# 限制访问来源
export WHISPER_ALLOWED_ORIGINS=http://localhost:3000,http://************
```

### 3. 数据保护

- 音频文件自动清理
- 不记录敏感内容
- 考虑加密传输（HTTPS）

## 生产部署

### 使用Systemd（Linux）

1. **创建服务文件**
```bash
sudo nano /etc/systemd/system/whisper.service
```

```ini
[Unit]
Description=Whisper Speech Recognition Service
After=network.target

[Service]
Type=simple
User=whisper
WorkingDirectory=/opt/whisper-server
Environment=WHISPER_MODEL_SIZE=base
ExecStart=/opt/whisper-server/venv/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

2. **启用服务**
```bash
sudo systemctl enable whisper
sudo systemctl start whisper
```

### 使用Nginx反向代理

```nginx
server {
    listen 80;
    server_name whisper.local;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 100M;
    }
}
```

## 总结

通过以上步骤，您应该能够成功在局域网内部署Whisper服务。这个方案提供了：

- ✅ 高准确度的语音识别
- ✅ 数据隐私保护（不出局域网）
- ✅ 可处理视频音频
- ✅ 支持多种语言
- ✅ 灵活的配置选项

如有问题，请参考故障排除部分或查看项目文档。
