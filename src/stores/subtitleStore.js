import { create } from 'zustand'
import speechRecognitionService from '../services/speechRecognitionService'
import videoAudioProcessor from '../services/videoAudioProcessor'

export const useSubtitleStore = create((set, get) => ({
  // 状态
  subtitles: [], // 字幕数组 [{id, startTime, endTime, text, translatedText}]
  currentVideoTime: 0,
  isRecording: false,
  currentLanguage: 'auto',
  targetLanguage: 'en',
  isTranslationEnabled: false,
  
  // 设置当前视频时间
  setCurrentVideoTime: (time) => set({ currentVideoTime: time }),
  
  // 添加字幕
  addSubtitle: (subtitle) => set((state) => ({
    subtitles: [...state.subtitles, {
      id: Date.now(),
      startTime: subtitle.startTime,
      endTime: subtitle.endTime || subtitle.startTime + 3, // 默认3秒持续时间
      text: subtitle.text,
      translatedText: subtitle.translatedText || '',
      ...subtitle
    }]
  })),
  
  // 更新字幕
  updateSubtitle: (id, updates) => set((state) => ({
    subtitles: state.subtitles.map(sub => 
      sub.id === id ? { ...sub, ...updates } : sub
    )
  })),
  
  // 删除字幕
  removeSubtitle: (id) => set((state) => ({
    subtitles: state.subtitles.filter(sub => sub.id !== id)
  })),
  
  // 清空所有字幕
  clearSubtitles: () => set({ subtitles: [] }),
  
  // 获取当前时间应显示的字幕
  getCurrentSubtitle: () => {
    const { subtitles, currentVideoTime } = get()
    return subtitles.find(sub => 
      currentVideoTime >= sub.startTime && currentVideoTime <= sub.endTime
    )
  },
  
  // 开始录音识别
  startRecording: async (videoElement = null) => {
    const { currentLanguage } = get()

    try {
      set({ isRecording: true })

      if (videoElement) {
        // 如果有视频元素，使用视频音频识别
        console.log('开始视频音频识别...')

        await speechRecognitionService.recognizeFromVideo(videoElement, {
          language: currentLanguage,
          onResult: (result) => {
            console.log('识别结果:', result)

            if (result.type === 'transcript' && result.text) {
              // 处理识别到的文本
              const currentTime = get().currentVideoTime
              const duration = Math.min(5, result.text.length * 0.1) // 根据文本长度估算持续时间

              get().addSubtitle({
                startTime: Math.max(0, currentTime - duration),
                endTime: currentTime,
                text: result.text
              })
            } else if (result.type === 'info') {
              // 显示信息提示
              console.log('提示信息:', result.message)
            }
          },
          onError: (error) => {
            console.error('视频语音识别错误:', error)
            alert(`语音识别失败: ${error.message || error}`)
            get().stopRecording()
          },
          onProgress: (progress) => {
            console.log('识别进度:', progress)
          }
        })
      } else {
        // 没有视频元素，回退到麦克风模式
        console.log('回退到麦克风识别模式...')

        if (!speechRecognitionService.isRecognitionSupported()) {
          alert('您的浏览器不支持语音识别功能，请使用Chrome或Edge浏览器')
          return
        }

        let lastFinalTranscript = ''
        let currentStartTime = get().currentVideoTime

        await speechRecognitionService.startMicrophoneRecognition({
          language: currentLanguage,
          onResult: (result) => {
            if (result.isFinal && result.finalTranscript && result.finalTranscript !== lastFinalTranscript) {
              const currentTime = get().currentVideoTime
              get().addSubtitle({
                startTime: currentStartTime,
                endTime: currentTime,
                text: result.finalTranscript
              })

              lastFinalTranscript = result.finalTranscript
              currentStartTime = currentTime
            }
          },
          onError: (error) => {
            console.error('语音识别错误:', error)
            if (error === 'not-allowed') {
              alert('请允许麦克风权限以使用语音识别功能')
            } else {
              alert('语音识别出现错误，请重试')
            }
            get().stopRecording()
          },
          onEnd: () => {
            // 如果还在录制状态，重新启动识别
            if (get().isRecording) {
              setTimeout(() => {
                if (get().isRecording) {
                  get().startRecording(videoElement)
                }
              }, 100)
            }
          }
        })
      }

    } catch (error) {
      console.error('启动语音识别失败:', error)
      alert(`无法启动语音识别: ${error.message || error}`)
      set({ isRecording: false })
    }
  },
  
  // 停止录音识别
  stopRecording: () => {
    speechRecognitionService.stopRecognition()
    // 同时停止视频音频处理
    videoAudioProcessor.stop()
    set({ isRecording: false })
  },

  // 清理所有资源
  cleanup: () => {
    speechRecognitionService.stopRecognition()
    videoAudioProcessor.cleanup()
    set({ isRecording: false })
  },

  // 演示模式 - 生成模拟字幕
  startDemoMode: () => {
    set({ isRecording: true })

    // 清空现有字幕
    get().clearSubtitles()

    // 获取演示字幕数据
    const demoSubtitles = speechRecognitionService.generateDemoSubtitles()

    // 预先生成所有演示字幕
    demoSubtitles.forEach(subtitle => {
      get().addSubtitle({
        startTime: subtitle.startTime,
        endTime: subtitle.startTime + subtitle.duration,
        text: subtitle.text
      })
    })
  },

  // 停止演示模式
  stopDemoMode: () => {
    set({ isRecording: false })
  },


  
  // 设置语言
  setCurrentLanguage: (language) => set({ currentLanguage: language }),
  setTargetLanguage: (language) => set({ targetLanguage: language }),
  
  // 切换翻译功能
  toggleTranslation: () => set((state) => ({ 
    isTranslationEnabled: !state.isTranslationEnabled 
  })),
  
  // 翻译字幕
  translateSubtitle: async (id) => {
    const { subtitles, targetLanguage } = get()
    const subtitle = subtitles.find(sub => sub.id === id)
    
    if (!subtitle || !subtitle.text) return
    
    try {
      // 这里可以集成真实的翻译API
      // 暂时使用模拟翻译
      const translatedText = await mockTranslate(subtitle.text, targetLanguage)
      
      get().updateSubtitle(id, { translatedText })
    } catch (error) {
      console.error('翻译失败:', error)
    }
  },
  
  // 批量翻译所有字幕
  translateAllSubtitles: async () => {
    const { subtitles } = get()
    
    for (const subtitle of subtitles) {
      if (subtitle.text && !subtitle.translatedText) {
        await get().translateSubtitle(subtitle.id)
        // 添加延迟避免API限制
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
  },
  
  // 导出字幕
  exportSubtitles: (format = 'srt') => {
    const { subtitles } = get()
    
    if (format === 'srt') {
      return exportToSRT(subtitles)
    } else if (format === 'vtt') {
      return exportToVTT(subtitles)
    }
    
    return ''
  }
}))

// 模拟翻译函数（实际项目中应该调用真实的翻译API）
const mockTranslate = async (text, targetLang) => {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 简单的模拟翻译
  const translations = {
    'zh-CN': {
      'en': text + ' (translated to English)',
      'ja': text + ' (翻訳された日本語)',
      'ko': text + ' (한국어로 번역됨)'
    },
    'en': {
      'zh-CN': text + ' (翻译成中文)',
      'ja': text + ' (日本語に翻訳)',
      'ko': text + ' (한국어로 번역)'
    }
  }
  
  return translations['zh-CN']?.[targetLang] || text
}

// 导出为SRT格式
const exportToSRT = (subtitles) => {
  return subtitles.map((sub, index) => {
    const startTime = formatSRTTime(sub.startTime)
    const endTime = formatSRTTime(sub.endTime)
    const text = sub.translatedText || sub.text
    
    return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`
  }).join('\n')
}

// 导出为VTT格式
const exportToVTT = (subtitles) => {
  const header = 'WEBVTT\n\n'
  const content = subtitles.map((sub, index) => {
    const startTime = formatVTTTime(sub.startTime)
    const endTime = formatVTTTime(sub.endTime)
    const text = sub.translatedText || sub.text
    
    return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`
  }).join('\n')
  
  return header + content
}

// 格式化SRT时间格式
const formatSRTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

// 格式化VTT时间格式
const formatVTTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}
