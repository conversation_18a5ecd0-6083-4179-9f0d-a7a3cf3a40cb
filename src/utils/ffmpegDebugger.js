/**
 * FFmpeg调试工具
 * 用于诊断FFmpeg音频提取问题
 */

import FFmpegAudioExtractor from '../services/ffmpegAudioExtractor.js'

class FFmpegDebugger {
  constructor() {
    this.extractor = new FFmpegAudioExtractor()
  }

  /**
   * 运行完整的诊断测试
   */
  async runDiagnostics(videoFile) {
    console.log('🔍 开始FFmpeg诊断测试...')
    
    const results = {
      fileValidation: false,
      ffmpegSupport: false,
      ffmpegInit: false,
      fileWrite: false,
      commandExec: false,
      audioExtraction: false,
      errors: []
    }

    try {
      // 1. 检查WebAssembly支持
      console.log('1️⃣ 检查WebAssembly支持...')
      results.ffmpegSupport = FFmpegAudioExtractor.isSupported()
      console.log(`WebAssembly支持: ${results.ffmpegSupport}`)

      if (!results.ffmpegSupport) {
        results.errors.push('浏览器不支持WebAssembly')
        return results
      }

      // 2. 验证文件
      console.log('2️⃣ 验证视频文件...')
      results.fileValidation = await this.validateFile(videoFile)
      
      if (!results.fileValidation) {
        results.errors.push('视频文件验证失败')
        return results
      }

      // 3. 初始化FFmpeg
      console.log('3️⃣ 初始化FFmpeg...')
      try {
        await this.extractor.initialize((progress) => {
          console.log(`初始化进度: ${progress.message}`)
        })
        results.ffmpegInit = true
        console.log('✅ FFmpeg初始化成功')
      } catch (error) {
        results.errors.push(`FFmpeg初始化失败: ${error.message}`)
        console.error('❌ FFmpeg初始化失败:', error)
        return results
      }

      // 4. 测试文件写入
      console.log('4️⃣ 测试文件写入...')
      results.fileWrite = await this.testFileWrite(videoFile)
      
      if (!results.fileWrite) {
        results.errors.push('文件写入测试失败')
        return results
      }

      // 5. 测试简单命令执行
      console.log('5️⃣ 测试FFmpeg命令执行...')
      results.commandExec = await this.testCommandExecution()
      
      if (!results.commandExec) {
        results.errors.push('FFmpeg命令执行测试失败')
        return results
      }

      // 6. 测试音频提取
      console.log('6️⃣ 测试音频提取...')
      results.audioExtraction = await this.testAudioExtraction(videoFile)

      console.log('🎉 诊断测试完成!')
      return results

    } catch (error) {
      console.error('❌ 诊断测试失败:', error)
      results.errors.push(`诊断测试异常: ${error.message}`)
      return results
    }
  }

  /**
   * 验证文件
   */
  async validateFile(videoFile) {
    try {
      if (!videoFile) {
        console.error('❌ 文件不存在')
        return false
      }

      if (videoFile.size === 0) {
        console.error('❌ 文件大小为0')
        return false
      }

      if (videoFile.size > 500 * 1024 * 1024) {
        console.error('❌ 文件过大 (>500MB)')
        return false
      }

      // 检查文件类型
      if (!videoFile.type.startsWith('video/')) {
        console.warn('⚠️ 文件MIME类型不是video/*:', videoFile.type)
      }

      // 尝试读取文件头部
      const reader = new FileReader()
      const readPromise = new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result)
        reader.onerror = () => reject(reader.error)
      })
      
      reader.readAsArrayBuffer(videoFile.slice(0, 1024))
      const buffer = await readPromise
      
      if (buffer.byteLength === 0) {
        console.error('❌ 无法读取文件内容')
        return false
      }

      console.log('✅ 文件验证通过')
      return true

    } catch (error) {
      console.error('❌ 文件验证失败:', error)
      return false
    }
  }

  /**
   * 测试文件写入
   */
  async testFileWrite(videoFile) {
    try {
      const { fetchFile } = await import('@ffmpeg/util')
      const testFileName = 'test_input.mp4'
      
      // 写入文件
      const fileData = await fetchFile(videoFile)
      await this.extractor.ffmpeg.writeFile(testFileName, fileData)
      
      // 验证文件是否存在
      const fileList = await this.extractor.ffmpeg.listDir('/')
      const writtenFile = fileList.find(f => f.name === testFileName)
      
      if (!writtenFile) {
        console.error('❌ 文件写入后未找到')
        return false
      }

      console.log(`✅ 文件写入成功: ${writtenFile.size} bytes`)
      
      // 清理测试文件
      await this.extractor.ffmpeg.deleteFile(testFileName)
      
      return true

    } catch (error) {
      console.error('❌ 文件写入测试失败:', error)
      return false
    }
  }

  /**
   * 测试命令执行
   */
  async testCommandExecution() {
    try {
      // 执行简单的版本查询命令
      await this.extractor.ffmpeg.exec(['-version'])
      console.log('✅ FFmpeg命令执行测试通过')
      return true

    } catch (error) {
      console.error('❌ FFmpeg命令执行测试失败:', error)
      return false
    }
  }

  /**
   * 测试音频提取
   */
  async testAudioExtraction(videoFile) {
    try {
      const result = await this.extractor.extractAudio(videoFile, {
        format: 'wav',
        sampleRate: 16000,
        channels: 1,
        duration: 5, // 只提取5秒
        onProgress: (progress) => {
          console.log(`提取进度: ${progress.message}`)
        }
      })

      if (result.success && result.audioBlob.size > 0) {
        console.log('✅ 音频提取测试成功')
        console.log(`音频大小: ${result.audioBlob.size} bytes`)
        return true
      } else {
        console.error('❌ 音频提取结果无效')
        return false
      }

    } catch (error) {
      console.error('❌ 音频提取测试失败:', error)
      return false
    }
  }

  /**
   * 生成诊断报告
   */
  generateReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      browser: navigator.userAgent,
      results: results,
      recommendations: []
    }

    // 生成建议
    if (!results.ffmpegSupport) {
      report.recommendations.push('请使用支持WebAssembly的现代浏览器')
    }

    if (!results.fileValidation) {
      report.recommendations.push('请检查视频文件是否有效且格式受支持')
    }

    if (!results.ffmpegInit) {
      report.recommendations.push('请检查网络连接和FFmpeg核心文件是否可访问')
    }

    if (!results.fileWrite) {
      report.recommendations.push('可能是内存不足或文件过大')
    }

    if (!results.commandExec) {
      report.recommendations.push('FFmpeg核心可能损坏，请重新下载')
    }

    if (!results.audioExtraction) {
      report.recommendations.push('视频格式可能不受支持，请尝试转换为MP4格式')
    }

    return report
  }
}

export default FFmpegDebugger
