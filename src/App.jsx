import React, { useState } from 'react'
import VideoPlayer from './components/VideoPlayer/VideoPlayer'
import SubtitleDisplay from './components/SubtitleDisplay/SubtitleDisplay'
import ControlPanel from './components/ControlPanel/ControlPanel'

function App() {
  const [isDemoMode, setIsDemoMode] = useState(true)

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            视频实时字幕生成器
          </h1>
          <p className="text-gray-600">
            上传视频，实时生成字幕，支持翻译和导出
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 视频播放区域 */}
          <div className="lg:col-span-2">
            <VideoPlayer isDemoMode={!isDemoMode} />
            <SubtitleDisplay />
          </div>

          {/* 控制面板 */}
          <div className="lg:col-span-1">
            <ControlPanel isDemoMode={!isDemoMode} setIsDemoMode={setIsDemoMode} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
