/**
 * 视频音频处理服务
 * 使用FFmpeg从视频中提取音频
 */

import FFmpegAudioExtractor from './ffmpegAudioExtractor.js'

class VideoAudioProcessor {
  constructor() {
    this.ffmpegExtractor = new FFmpegAudioExtractor()
    this.isProcessing = false
  }

  /**
   * 从视频文件中提取音频（使用FFmpeg）
   * @param {File} videoFile - 视频文件
   * @param {Object} options - 配置选项
   */
  async extractAudioFromFile(videoFile, options = {}) {
    const {
      onProgress = () => {},
      onComplete = () => {},
      onError = () => {},
      format = 'wav',
      sampleRate = 16000,
      duration = null,  // 提取时长，null表示全部
      startTime = 0     // 开始时间
    } = options

    try {
      this.isProcessing = true
      console.log('🎵 开始使用FFmpeg提取音频')
      console.log(`📁 视频文件: ${videoFile.name}`)
      console.log(`🎛️ 输出格式: ${format}, 采样率: ${sampleRate}Hz`)

      // 提取音频
      const result = await this.ffmpegExtractor.extractAudio(videoFile, {
        format,
        sampleRate,
        channels: 1, // 单声道
        startTime,
        duration,
        onProgress
      })

      this.isProcessing = false
      console.log('✅ 音频提取完成')

      // 调用完成回调
      onComplete(result)

      return result

    } catch (error) {
      this.isProcessing = false
      console.error('❌ 音频提取失败:', error)
      onError(error)
      throw error
    }
  }



  /**
   * 停止音频处理
   */
  stop() {
    this.isProcessing = false
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.stop()
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      ffmpegLoaded: this.ffmpegExtractor.isLoaded
    }
  }
}

export default new VideoAudioProcessor()
