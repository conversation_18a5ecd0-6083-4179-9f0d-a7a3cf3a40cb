/**
 * 云端语音识别服务
 * 集成多个云端语音识别API
 */

class CloudSpeechRecognition {
  constructor() {
    this.apiKeys = {
      google: process.env.VITE_GOOGLE_SPEECH_API_KEY,
      azure: process.env.VITE_AZURE_SPEECH_API_KEY,
      baidu: process.env.VITE_BAIDU_SPEECH_API_KEY,
      openai: process.env.VITE_OPENAI_API_KEY
    }
    this.currentProvider = 'google'
  }

  /**
   * 设置API提供商
   * @param {string} provider - API提供商 (google, azure, baidu, openai)
   */
  setProvider(provider) {
    if (['google', 'azure', 'baidu', 'openai'].includes(provider)) {
      this.currentProvider = provider
    }
  }

  /**
   * 识别音频文件
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {Object} options - 识别选项
   */
  async recognizeAudio(audioData, options = {}) {
    const {
      language = 'zh-CN',
      onProgress = () => {},
      onResult = () => {}
    } = options

    try {
      switch (this.currentProvider) {
        case 'google':
          return await this.recognizeWithGoogle(audioData, language, options)
        case 'azure':
          return await this.recognizeWithAzure(audioData, language, options)
        case 'baidu':
          return await this.recognizeWithBaidu(audioData, language, options)
        case 'openai':
          return await this.recognizeWithOpenAI(audioData, language, options)
        default:
          throw new Error('不支持的API提供商')
      }
    } catch (error) {
      console.error('云端识别失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * Google Speech-to-Text API
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {string} language - 语言代码
   * @param {Object} options - 选项
   */
  async recognizeWithGoogle(audioData, language, options) {
    if (!this.apiKeys.google) {
      throw new Error('Google API密钥未配置')
    }

    // 转换音频为base64
    const base64Audio = await this.audioToBase64(audioData)

    const requestBody = {
      config: {
        encoding: 'WEBM_OPUS',
        sampleRateHertz: 16000,
        languageCode: this.convertLanguageCode(language, 'google'),
        enableAutomaticPunctuation: true,
        enableWordTimeOffsets: true,
        model: 'latest_long'
      },
      audio: {
        content: base64Audio
      }
    }

    const response = await fetch(
      `https://speech.googleapis.com/v1/speech:recognize?key=${this.apiKeys.google}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      }
    )

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error?.message || '识别失败')
    }

    return this.formatGoogleResult(result)
  }

  /**
   * Azure Speech Services API
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {string} language - 语言代码
   * @param {Object} options - 选项
   */
  async recognizeWithAzure(audioData, language, options) {
    if (!this.apiKeys.azure) {
      throw new Error('Azure API密钥未配置')
    }

    const region = 'eastus' // 可配置
    const endpoint = `https://${region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1`

    const params = new URLSearchParams({
      language: this.convertLanguageCode(language, 'azure'),
      format: 'detailed'
    })

    const response = await fetch(`${endpoint}?${params}`, {
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': this.apiKeys.azure,
        'Content-Type': 'audio/wav'
      },
      body: audioData
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error?.message || '识别失败')
    }

    return this.formatAzureResult(result)
  }

  /**
   * 百度语音识别API
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {string} language - 语言代码
   * @param {Object} options - 选项
   */
  async recognizeWithBaidu(audioData, language, options) {
    if (!this.apiKeys.baidu) {
      throw new Error('百度API密钥未配置')
    }

    // 首先获取access_token
    const accessToken = await this.getBaiduAccessToken()

    // 转换音频为base64
    const base64Audio = await this.audioToBase64(audioData)

    const requestBody = {
      format: 'webm',
      rate: 16000,
      channel: 1,
      cuid: 'web_client',
      token: accessToken,
      speech: base64Audio,
      len: audioData.size || audioData.byteLength
    }

    const response = await fetch('https://vop.baidu.com/server_api', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    const result = await response.json()

    if (result.err_no !== 0) {
      throw new Error(result.err_msg || '识别失败')
    }

    return this.formatBaiduResult(result)
  }

  /**
   * OpenAI Whisper API
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {string} language - 语言代码
   * @param {Object} options - 选项
   */
  async recognizeWithOpenAI(audioData, language, options) {
    if (!this.apiKeys.openai) {
      throw new Error('OpenAI API密钥未配置')
    }

    const formData = new FormData()
    formData.append('file', new Blob([audioData], { type: 'audio/webm' }), 'audio.webm')
    formData.append('model', 'whisper-1')
    formData.append('language', this.convertLanguageCode(language, 'openai'))
    formData.append('response_format', 'verbose_json')
    formData.append('timestamp_granularities[]', 'word')

    const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKeys.openai}`
      },
      body: formData
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error?.message || '识别失败')
    }

    return this.formatOpenAIResult(result)
  }

  /**
   * 获取百度Access Token
   */
  async getBaiduAccessToken() {
    const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: this.apiKeys.baidu.appId,
        client_secret: this.apiKeys.baidu.appSecret
      })
    })

    const result = await response.json()
    return result.access_token
  }

  /**
   * 转换音频为Base64
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   */
  async audioToBase64(audioData) {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = reader.result.split(',')[1]
        resolve(base64)
      }
      
      if (audioData instanceof ArrayBuffer) {
        reader.readAsDataURL(new Blob([audioData]))
      } else {
        reader.readAsDataURL(audioData)
      }
    })
  }

  /**
   * 转换语言代码
   * @param {string} language - 标准语言代码
   * @param {string} provider - API提供商
   */
  convertLanguageCode(language, provider) {
    const languageMap = {
      google: {
        'zh-CN': 'zh-CN',
        'en-US': 'en-US',
        'ja-JP': 'ja-JP',
        'ko-KR': 'ko-KR'
      },
      azure: {
        'zh-CN': 'zh-CN',
        'en-US': 'en-US',
        'ja-JP': 'ja-JP',
        'ko-KR': 'ko-KR'
      },
      baidu: {
        'zh-CN': 1537,
        'en-US': 1737,
        'ja-JP': 1637,
        'ko-KR': 1837
      },
      openai: {
        'zh-CN': 'zh',
        'en-US': 'en',
        'ja-JP': 'ja',
        'ko-KR': 'ko'
      }
    }

    return languageMap[provider]?.[language] || languageMap[provider]?.[language.split('-')[0]]
  }

  /**
   * 格式化Google结果
   */
  formatGoogleResult(result) {
    const alternatives = result.results?.[0]?.alternatives?.[0]
    if (!alternatives) {
      return { success: false, error: '无识别结果' }
    }

    return {
      success: true,
      text: alternatives.transcript,
      confidence: alternatives.confidence,
      words: alternatives.words?.map(word => ({
        word: word.word,
        startTime: parseFloat(word.startTime?.replace('s', '') || 0),
        endTime: parseFloat(word.endTime?.replace('s', '') || 0)
      })) || []
    }
  }

  /**
   * 格式化Azure结果
   */
  formatAzureResult(result) {
    if (result.RecognitionStatus !== 'Success') {
      return { success: false, error: '识别失败' }
    }

    return {
      success: true,
      text: result.DisplayText,
      confidence: result.NBest?.[0]?.Confidence || 0,
      words: result.NBest?.[0]?.Words?.map(word => ({
        word: word.Word,
        startTime: word.Offset / 10000000, // 转换为秒
        endTime: (word.Offset + word.Duration) / 10000000
      })) || []
    }
  }

  /**
   * 格式化百度结果
   */
  formatBaiduResult(result) {
    return {
      success: true,
      text: result.result?.[0] || '',
      confidence: 1.0, // 百度API不返回置信度
      words: []
    }
  }

  /**
   * 格式化OpenAI结果
   */
  formatOpenAIResult(result) {
    return {
      success: true,
      text: result.text,
      confidence: 1.0, // Whisper不返回置信度
      words: result.words?.map(word => ({
        word: word.word,
        startTime: word.start,
        endTime: word.end
      })) || []
    }
  }
}

export default new CloudSpeechRecognition()
