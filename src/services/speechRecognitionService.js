import localWhisperService from './localWhisperService'
import videoAudioProcessor from './videoAudioProcessor'
// 暂时注释掉其他导入
// import offlineSpeechRecognition from './offlineSpeechRecognition'
// import cloudSpeechRecognition from './cloudSpeechRecognition'

/**
 * 语音识别服务
 * 提供多种语音识别方案的统一接口
 */

class SpeechRecognitionService {
  constructor() {
    this.recognition = null
    this.isSupported = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
    this.audioContext = null
    this.mediaRecorder = null
    this.currentMode = 'browser' // browser, offline, cloud, local
    this.availableModes = this.detectAvailableModes()
  }

  /**
   * 检测可用的识别模式
   */
  detectAvailableModes() {
    const modes = []

    // 局域网Whisper支持（需要配置）
    modes.push('local')

    // 浏览器原生支持
    if (this.isSupported) {
      modes.push('browser')
    }

    // WebAssembly离线支持
    if (typeof WebAssembly !== 'undefined') {
      modes.push('offline')
    }

    // 云端API支持（总是可用，但需要网络）
    modes.push('cloud')

    return modes
  }

  /**
   * 设置识别模式
   * @param {string} mode - 识别模式 (browser, offline, cloud)
   */
  setRecognitionMode(mode) {
    if (this.availableModes.includes(mode)) {
      this.currentMode = mode
      return true
    }
    return false
  }

  /**
   * 获取当前可用的识别模式
   */
  getAvailableModes() {
    return this.availableModes.map(mode => ({
      id: mode,
      name: this.getModeDisplayName(mode),
      description: this.getModeDescription(mode),
      available: this.isModeAvailable(mode)
    }))
  }

  /**
   * 获取模式显示名称
   */
  getModeDisplayName(mode) {
    const names = {
      local: '局域网识别',
      browser: '浏览器识别',
      offline: '离线识别',
      cloud: '云端识别'
      
    }
    return names[mode] || mode
  }

  /**
   * 获取模式描述
   */
  getModeDescription(mode) {
    const descriptions = {
      local: '使用局域网Whisper服务，高准确度，数据不出局域网',
      browser: '使用浏览器内置语音识别，需要麦克风权限',
      offline: '使用WebAssembly离线识别，可处理视频音频',
      cloud: '使用云端API识别，准确度最高，需要网络连接'
    }
    return descriptions[mode] || ''
  }

  /**
   * 检查模式是否可用
   */
  isModeAvailable(mode) {
    switch (mode) {
      case 'browser':
        return this.isSupported
      case 'offline':
        return typeof WebAssembly !== 'undefined' // && offlineSpeechRecognition.isSupported()
      case 'cloud':
        return navigator.onLine
      case 'local':
        return localWhisperService.isServiceConnected()
      default:
        return false
    }
  }

  /**
   * 检查浏览器是否支持语音识别
   */
  isRecognitionSupported() {
    return this.isSupported
  }

  /**
   * 启动麦克风语音识别
   * @param {Object} options - 配置选项
   * @param {string} options.language - 识别语言
   * @param {Function} options.onResult - 结果回调
   * @param {Function} options.onError - 错误回调
   * @param {Function} options.onEnd - 结束回调
   */
  async startMicrophoneRecognition(options = {}) {
    if (!this.isSupported) {
      throw new Error('浏览器不支持语音识别功能')
    }

    const {
      language = 'auto',
      onResult = () => {},
      onError = () => {},
      onEnd = () => {}
    } = options

    try {
      // 请求麦克风权限
      await navigator.mediaDevices.getUserMedia({ audio: true })

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      this.recognition = new SpeechRecognition()

      this.recognition.continuous = true
      this.recognition.interimResults = true
      this.recognition.lang = language

      this.recognition.onresult = (event) => {
        let interimTranscript = ''
        let finalTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        onResult({
          finalTranscript: finalTranscript.trim(),
          interimTranscript: interimTranscript.trim(),
          isFinal: finalTranscript.length > 0
        })
      }

      this.recognition.onerror = (event) => {
        console.error('语音识别错误:', event.error)
        onError(event.error)
      }

      this.recognition.onend = () => {
        onEnd()
      }

      this.recognition.start()
      return true

    } catch (error) {
      console.error('启动麦克风识别失败:', error)
      throw error
    }
  }

  /**
   * 停止语音识别
   */
  stopRecognition() {
    if (this.recognition) {
      this.recognition.stop()
      this.recognition = null
    }

    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop()
      this.mediaRecorder = null
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }
  }

  /**
   * 从视频中识别语音
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {Object} options - 配置选项
   */
  async recognizeFromVideo(videoElement, options = {}) {
    const {
      language = 'auto',
      onResult = () => {},
      onError = () => {},
      onProgress = () => {}
    } = options

    try {
      switch (this.currentMode) {
        case 'browser':
          // 浏览器模式：提取音频 + 麦克风识别提示
          return await this.recognizeVideoWithBrowser(videoElement, { ...options, language, onProgress })

        case 'local':
          // 局域网模式：提取音频 + 局域网Whisper识别
          return await this.recognizeVideoWithLocal(videoElement, { ...options, language, onProgress })

        case 'offline':
          // 离线模式暂不支持
          onResult({
            type: 'info',
            message: '离线模式暂不支持，请使用局域网模式或浏览器模式'
          })
          return { success: false, error: '离线模式暂不支持' }

        case 'cloud':
          // 云端模式暂不支持
          onResult({
            type: 'info',
            message: '云端模式暂不支持，请使用局域网模式或浏览器模式'
          })
          return { success: false, error: '云端模式暂不支持' }

        default:
          throw new Error('不支持的识别模式')
      }
    } catch (error) {
      console.error('视频语音识别失败:', error)
      onError(error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 浏览器模式视频识别
   */
  async recognizeVideoWithBrowser(videoElement, options) {
    const { onResult, onError } = options

    // 提取音频数据用于分析
    const audioResult = await videoAudioProcessor.extractAudioFromVideo(videoElement, {
      onAudioData: (data) => {
        // 可以显示音频波形或音量指示
        if (data.speechDetected) {
          console.log('检测到语音活动')
        }
      },
      onError: onError
    })

    if (!audioResult.success) {
      // 回退到麦克风模式
      console.warn('音频提取失败，回退到麦克风模式')
      return await this.startMicrophoneRecognition(options)
    }

    // 提示用户使用麦克风进行识别
    onResult({
      type: 'info',
      message: '已提取视频音频，请使用麦克风模式进行语音识别，或考虑使用局域网识别模式'
    })

    return { success: true, mode: 'browser_with_audio_extraction' }
  }

  /**
   * 局域网模式视频识别
   */
  async recognizeVideoWithLocal(videoElement, options) {
    const { onResult, onError, onProgress, language } = options

    try {
      // 检查局域网服务连接
      if (!localWhisperService.isServiceConnected()) {
        throw new Error('局域网Whisper服务未连接')
      }

      console.log('🚀 开始局域网模式视频识别（使用FFmpeg）...')
      onProgress({ stage: 'preparing', progress: 5, message: '准备提取音频...' })

      // 从视频元素获取文件
      if (!videoElement.src || !videoElement.src.startsWith('blob:')) {
        throw new Error('视频元素必须有有效的文件源')
      }

      console.log('📹 从视频元素获取文件进行FFmpeg提取')

      // 从blob URL获取文件
      const response = await fetch(videoElement.src)
      const videoBlob = await response.blob()
      const videoFile = new File([videoBlob], 'video.mp4', { type: videoBlob.type })

      console.log(`📁 视频文件大小: ${(videoFile.size/1024/1024).toFixed(2)}MB`)

      // 使用FFmpeg提取音频
      const audioResult = await videoAudioProcessor.extractAudioFromFile(videoFile, {
        format: 'wav',
        sampleRate: 16000,
        duration: 5 * 60, // 5分钟
        onProgress: (progress) => {
          // 将FFmpeg进度映射到总进度的前50%
          const mappedProgress = 5 + (progress.progress || 0) * 0.45
          onProgress({
            stage: 'extracting',
            progress: mappedProgress,
            message: progress.message || '使用FFmpeg提取音频中...'
          })
        },
        onComplete: async (audioData) => {
          console.log('🎵 FFmpeg音频提取完成，开始Whisper识别...', {
            size: audioData.size,
            format: audioData.format,
            sampleRate: audioData.sampleRate
          })

          onProgress({ stage: 'uploading', progress: 55, message: '上传音频到Whisper服务...' })

          try {
            // 使用局域网Whisper服务识别
            const recognitionResult = await localWhisperService.transcribe(
              audioData.audioBlob,
              {
                language: language === 'zh-CN' ? 'zh' : language.split('-')[0],
                onProgress: (whisperProgress) => {
                  // 将Whisper进度映射到总进度的后45%
                  const mappedProgress = 55 + (whisperProgress.progress || 0) * 0.45
                  onProgress({
                    stage: 'recognizing',
                    progress: mappedProgress,
                    message: whisperProgress.message || '语音识别中...'
                  })
                }
              }
            )

            console.log('✅ Whisper识别完成:', recognitionResult)

            if (recognitionResult.success && recognitionResult.text) {
              onProgress({ stage: 'complete', progress: 100, message: '识别完成' })
              onResult({
                type: 'transcript',
                text: recognitionResult.text,
                language: recognitionResult.language,
                segments: recognitionResult.segments,
                processingTime: recognitionResult.processingTime
              })
            } else {
              console.warn('识别结果为空或失败:', recognitionResult)
              onError(new Error('识别结果为空'))
            }
          } catch (error) {
            console.error('❌ Whisper识别失败:', error)
            onError(error)
          }
        },
        onError: (error) => {
          console.error('❌ FFmpeg音频提取失败:', error)
          onError(error)
        }
      })

      return audioResult
    } catch (error) {
      console.error('局域网识别失败:', error)
      throw error
    }
  }

  /**
   * 设置局域网Whisper服务地址
   * @param {string} serverUrl - 服务器地址
   */
  async setLocalWhisperServer(serverUrl) {
    try {
      localWhisperService.setServerUrl(serverUrl)
      await localWhisperService.connect()
      return true
    } catch (error) {
      console.error('连接局域网Whisper服务失败:', error)
      throw error
    }
  }

  /**
   * 自动发现局域网Whisper服务
   */
  async discoverLocalWhisperService() {
    try {
      const service = await localWhisperService.discoverService()
      return service
    } catch (error) {
      console.error('发现局域网Whisper服务失败:', error)
      throw error
    }
  }

  /**
   * 获取局域网服务状态
   */
  async getLocalWhisperStatus() {
    return await localWhisperService.getStatus()
  }

  /**
   * 测试连接到指定服务器
   */
  async testConnection(serverUrl) {
    try {
      const tempUrl = serverUrl.startsWith('http') ? serverUrl : `http://${serverUrl}`
      const result = await localWhisperService.checkService(tempUrl)
      return result
    } catch (error) {
      console.error('测试连接失败:', error)
      return { available: false, error: error.message }
    }
  }



  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages() {
    return [
      { code: 'zh-CN', name: '中文（简体）' },
      { code: 'zh-TW', name: '中文（繁体）' },
      { code: 'en-US', name: 'English (US)' },
      { code: 'en-GB', name: 'English (UK)' },
      { code: 'ja-JP', name: '日本語' },
      { code: 'ko-KR', name: '한국어' },
      { code: 'es-ES', name: 'Español' },
      { code: 'fr-FR', name: 'Français' },
      { code: 'de-DE', name: 'Deutsch' },
      { code: 'ru-RU', name: 'Русский' },
      { code: 'it-IT', name: 'Italiano' },
      { code: 'pt-BR', name: 'Português (Brasil)' },
      { code: 'ar-SA', name: 'العربية' },
      { code: 'hi-IN', name: 'हिन्दी' }
    ]
  }

  /**
   * 生成演示字幕数据
   */
  generateDemoSubtitles() {
    return [
      { text: "欢迎使用视频实时字幕生成器", startTime: 2, duration: 3 },
      { text: "这是一个功能演示", startTime: 6, duration: 3 },
      { text: "您可以上传任意视频文件", startTime: 10, duration: 3 },
      { text: "系统支持多种语言识别", startTime: 14, duration: 3 },
      { text: "可以实时生成和编辑字幕", startTime: 18, duration: 3 },
      { text: "支持字幕翻译功能", startTime: 22, duration: 3 },
      { text: "可以导出SRT和VTT格式", startTime: 26, duration: 3 },
      { text: "感谢您的使用！", startTime: 30, duration: 3 }
    ]
  }
}

export default new SpeechRecognitionService()
