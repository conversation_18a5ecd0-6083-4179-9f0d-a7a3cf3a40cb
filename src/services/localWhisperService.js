/**
 * 局域网Whisper服务客户端
 * 用于调用局域网内的Whisper语音识别服务
 */

class LocalWhisperService {
  constructor() {
    this.serverUrl = null
    this.isConnected = false
    this.serverInfo = null
    this.defaultTimeout = 5*60000 // 5分钟超时
  }

  /**
   * 设置服务器地址
   * @param {string} url - 服务器URL (例: http://*************:8000)
   */
  setServerUrl(url) {
    // 确保URL格式正确
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'http://' + url
    }

    // 移除末尾的斜杠
    this.serverUrl = url.replace(/\/$/, '')
    this.isConnected = false
    this.serverInfo = null

    console.log('设置服务器地址:', this.serverUrl)
  }

  /**
   * 自动发现局域网内的Whisper服务
   * @param {Array} ipRanges - IP范围列表
   * @param {number} port - 端口号
   */
  async discoverService(ipRanges = ['192.168.5'], port = 8000) {
    console.log('正在搜索局域网内的Whisper服务...');
    
    const promises = []
    
    // 生成要检查的IP地址列表
    for (const range of ipRanges) {
      for (let i = 1; i <= 254; i++) {
        const ip = `${range}.${i}`
        const url = `http://${ip}:${port}`
        
        promises.push(
          this.checkService(url).then(result => {
            if (result.available) {
              return { url, info: result.info }
            }
            return null
          }).catch(() => null)
        )
      }
    }

    // 并发检查所有IP，但限制并发数
    const results = await this.batchPromises(promises, 20)
    const availableServices = results.filter(result => result !== null)

    if (availableServices.length > 0) {
      // 选择第一个可用的服务
      const service = availableServices[0]
      this.setServerUrl(service.url)
      this.serverInfo = service.info
      this.isConnected = true
      
      console.log(`发现Whisper服务: ${service.url}`)
      return service
    }

    throw new Error('未发现可用的Whisper服务')
  }

  /**
   * 批量执行Promise，限制并发数
   */
  async batchPromises(promises, batchSize) {
    const results = []
    
    for (let i = 0; i < promises.length; i += batchSize) {
      const batch = promises.slice(i, i + batchSize)
      const batchResults = await Promise.allSettled(batch)
      
      results.push(...batchResults.map(result => 
        result.status === 'fulfilled' ? result.value : null
      ))
    }
    
    return results
  }

  /**
   * 检查服务是否可用
   * @param {string} url - 服务URL
   */
  async checkService(url) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 2000) // 2秒超时

      const response = await fetch(`${url}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json'
        }
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const info = await response.json()
        return {
          available: true,
          info: info
        }
      }
      
      return { available: false }
    } catch (error) {
      return { available: false }
    }
  }

  /**
   * 连接到服务器并获取信息
   */
  async connect() {
    if (!this.serverUrl) {
      throw new Error('请先设置服务器地址')
    }

    try {
      // 检查健康状态
      const healthResponse = await fetch(`${this.serverUrl}/health`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!healthResponse.ok) {
        throw new Error(`服务器健康检查失败: ${healthResponse.status}`)
      }

      // 获取服务信息
      const infoResponse = await fetch(`${this.serverUrl}/info`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
      })

      if (!infoResponse.ok) {
        throw new Error(`获取服务信息失败: ${infoResponse.status}`)
      }

      this.serverInfo = await infoResponse.json()
      this.isConnected = true

      console.log('已连接到Whisper服务:', this.serverInfo)
      return this.serverInfo

    } catch (error) {
      this.isConnected = false
      console.error('连接Whisper服务失败:', error)
      throw error
    }
  }

  /**
   * 语音识别
   * @param {Blob|ArrayBuffer} audioData - 音频数据
   * @param {Object} options - 识别选项
   */
  async transcribe(audioData, options = {}) {
    if (!this.isConnected) {
      await this.connect()
    }

    const {
      language = 'auto',
      task = 'transcribe', // transcribe 或 translate
      onProgress = () => {}
    } = options

    try {
      // 准备FormData
      const formData = new FormData()
      
      // 添加音频文件
      if (audioData instanceof Blob) {
        formData.append('audio', audioData, 'audio.wav')
      } else if (audioData instanceof ArrayBuffer) {
        const blob = new Blob([audioData], { type: 'audio/webm' })
        formData.append('audio', blob, 'audio.webm')
      } else {
        throw new Error('不支持的音频数据格式')
      }

      // 添加参数
      formData.append('language', language)
      formData.append('task', task)

      onProgress({ stage: 'uploading', progress: 0 })

      // 发送请求
      const response = await fetch(`${this.serverUrl}/transcribe`, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(this.defaultTimeout)
      })

      onProgress({ stage: 'processing', progress: 50 })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `请求失败: ${response.status}`)
      }

      const result = await response.json()
      onProgress({ stage: 'completed', progress: 100 })

      if (!result.success) {
        throw new Error(result.error || '识别失败')
      }

      return {
        success: true,
        text: result.text,
        language: result.language,
        segments: result.segments || [],
        processingTime: result.processing_time,
        audioDuration: result.audio_duration
      }

    } catch (error) {
      console.error('语音识别失败:', error)
      
      // 如果是网络错误，标记为未连接
      if (error.name === 'TypeError' || error.name === 'AbortError') {
        this.isConnected = false
      }

      throw error
    }
  }

  /**
   * 获取服务器状态
   */
  async getStatus() {
    if (!this.serverUrl) {
      return { connected: false, error: '未设置服务器地址' }
    }

    try {
      const response = await fetch(`${this.serverUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      })

      if (response.ok) {
        const data = await response.json()
        return {
          connected: true,
          status: data,
          serverInfo: this.serverInfo
        }
      } else {
        return {
          connected: false,
          error: `服务器响应错误: ${response.status}`
        }
      }
    } catch (error) {
      return {
        connected: false,
        error: error.message
      }
    }
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages() {
    return [
      { code: 'auto', name: '自动检测' },
      { code: 'zh', name: '中文' },
      { code: 'en', name: 'English' },
      { code: 'ja', name: '日本語' },
      { code: 'ko', name: '한국어' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'ru', name: 'Русский' },
      { code: 'it', name: 'Italiano' },
      { code: 'pt', name: 'Português' },
      { code: 'ar', name: 'العربية' },
      { code: 'hi', name: 'हिन्दी' },
      { code: 'th', name: 'ไทย' },
      { code: 'vi', name: 'Tiếng Việt' }
    ]
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.isConnected = false
    this.serverInfo = null
    console.log('已断开Whisper服务连接')
  }

  /**
   * 检查是否已连接
   */
  isServiceConnected() {
    return this.isConnected && this.serverUrl
  }

  /**
   * 获取服务器信息
   */
  getServerInfo() {
    return this.serverInfo
  }
}

export default new LocalWhisperService()
