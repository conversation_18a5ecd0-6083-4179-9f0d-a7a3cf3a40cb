/**
 * FFmpeg音频提取服务
 * 使用FFmpeg.wasm在浏览器中提取视频音频
 */

class FFmpegAudioExtractor {
  constructor() {
    this.ffmpeg = null
    this.isLoaded = false
    this.isLoading = false
  }

  /**
   * 初始化FFmpeg
   */
  async initialize(onProgress = () => {}) {
    if (this.isLoaded) return true;

    if (this.isLoading) {
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.isLoaded;
    }

    try {
      this.isLoading = true;
      console.log('🚀 开始初始化FFmpeg...');
      onProgress({ stage: 'loading', message: '正在加载FFmpeg模块...' });

      // 动态导入FFmpeg模块
      const { FFmpeg } = await import('@ffmpeg/ffmpeg');
      const { toBlobURL } = await import('@ffmpeg/util');

      // 创建FFmpeg实例
      this.ffmpeg = new FFmpeg();

      // 设置日志处理
      this.ffmpeg.on('log', ({ message }) => {
        console.log('[FFmpeg]', message);
      });

      // 设置进度处理
      this.ffmpeg.on('progress', ({ progress }) => {
        if (progress > 0) {
          onProgress({
            stage: 'processing',
            progress: progress * 100,
            message: `处理进度: ${(progress * 100).toFixed(1)}%`
          });
        }
      });

      onProgress({ stage: 'loading', message: '正在加载FFmpeg核心文件...' });

      // 使用本地FFmpeg文件
      const baseURL = '/ffmpeg';

      await this.ffmpeg.load({
        coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
        wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
      });

      this.isLoaded = true;
      this.isLoading = false;

      onProgress({ stage: 'ready', message: 'FFmpeg初始化完成' });
      console.log('✅ FFmpeg初始化成功');

      return true;

    } catch (error) {
      this.isLoading = false;
      this.isLoaded = false;
      console.error('❌ FFmpeg初始化失败:', error);
      onProgress({ stage: 'error', message: `初始化失败: ${error.message}` });
      throw error;
    }
  }

  /**
   * 从视频文件提取音频
   */
  async extractAudio(videoFile, options = {}) {
    const {
      format = 'wav',
      sampleRate = 16000,
      channels = 1,
      startTime = 0,
      duration = null,
      onProgress = () => {}
    } = options

    // 确保FFmpeg已初始化
    if (!this.isLoaded) {
      await this.initialize(onProgress)
    }

    try {
      console.log('🎵 开始音频提取')
      console.log(`📁 输入文件: ${videoFile.name} (${(videoFile.size/1024/1024).toFixed(2)}MB)`)

      onProgress({ stage: 'preparing', message: '准备文件...' })

      // 生成文件名
      const inputName = `input.${this.getFileExtension(videoFile.name)}`
      const outputName = `output.${format}`

      console.log(`📝 输入文件名: ${inputName}`)
      console.log(`📝 输出文件名: ${outputName}`)

      // 写入输入文件
      const { fetchFile } = await import('@ffmpeg/util')
      await this.ffmpeg.writeFile(inputName, await fetchFile(videoFile))
      console.log('✅ 输入文件写入完成')

      // 构建FFmpeg命令
      const args = ['-i', inputName]

      // 添加开始时间
      if (startTime > 0) {
        args.push('-ss', startTime.toString())
      }

      // 添加持续时间
      if (duration !== null) {
        args.push('-t', duration.toString())
      }

      // 音频处理参数
      args.push(
        '-vn',                              // 不处理视频
        '-acodec', this.getAudioCodec(format), // 音频编码器
        '-ar', sampleRate.toString(),       // 采样率
        '-ac', channels.toString(),         // 声道数
        '-y',                               // 覆盖输出文件
        outputName
      )

      console.log('🔧 FFmpeg命令:', args.join(' '))
      onProgress({ stage: 'processing', message: '正在提取音频...' })

      // 执行FFmpeg命令
      await this.ffmpeg.exec(args)
      console.log('✅ FFmpeg命令执行完成')

      onProgress({ stage: 'reading', message: '读取输出文件...' })

      // 读取输出文件
      const data = await this.ffmpeg.readFile(outputName)
      console.log(`📊 输出文件大小: ${data.length} bytes`)

      // 创建Blob
      const mimeType = this.getMimeType(format)
      const audioBlob = new Blob([data], { type: mimeType })

      // 清理临时文件
      try {
        await this.ffmpeg.deleteFile(inputName)
        await this.ffmpeg.deleteFile(outputName)
        console.log('🗑️ 临时文件清理完成')
      } catch (e) {
        console.warn('清理临时文件失败:', e)
      }

      const result = {
        success: true,
        audioBlob: audioBlob,
        format: format,
        mimeType: mimeType,
        size: audioBlob.size,
        sampleRate: sampleRate,
        channels: channels,
        duration: duration,
        originalFile: videoFile.name
      }

      console.log('✅ 音频提取完成')
      console.log(`📊 输出大小: ${(audioBlob.size/1024/1024).toFixed(2)}MB`)

      onProgress({ stage: 'complete', message: '音频提取完成' })
      return result

    } catch (error) {
      console.error('❌ 音频提取失败:', error)
      onProgress({ stage: 'error', message: `提取失败: ${error.message}` })
      throw error
    }
  }

  /**
   * 获取文件扩展名
   */
  getFileExtension(filename) {
    const ext = filename.split('.').pop().toLowerCase()
    return ext || 'mp4'
  }

  /**
   * 获取音频编码器
   */
  getAudioCodec(format) {
    const codecs = {
      'wav': 'pcm_s16le',
      'mp3': 'libmp3lame',
      'm4a': 'aac',
      'ogg': 'libvorbis'
    }
    return codecs[format] || 'pcm_s16le'
  }

  /**
   * 获取MIME类型
   */
  getMimeType(format) {
    const mimeTypes = {
      'wav': 'audio/wav',
      'mp3': 'audio/mpeg',
      'm4a': 'audio/mp4',
      'ogg': 'audio/ogg'
    }
    return mimeTypes[format] || 'audio/wav'
  }

  /**
   * 下载音频文件
   */
  downloadAudio(audioBlob, filename, format) {
    const url = URL.createObjectURL(audioBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}.${format}`
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)

    setTimeout(() => {
      URL.revokeObjectURL(url)
    }, 1000)

    console.log(`💾 音频文件已下载: ${filename}.${format}`)
  }

  /**
   * 检查是否支持
   */
  static isSupported() {
    return typeof WebAssembly === 'object'
  }
}

export default FFmpegAudioExtractor