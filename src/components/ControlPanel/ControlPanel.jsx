import React, { useState, useEffect } from 'react'
import { Download, Trash2, Globe, Mic, MicOff, Settings, Cpu, Cloud, Monitor, Server } from 'lucide-react'
import { saveAs } from 'file-saver'
import { useSubtitleStore } from '../../stores/subtitleStore'
import speechRecognitionService from '../../services/speechRecognitionService'
import LocalWhisperConfig from '../LocalWhisperConfig/LocalWhisperConfig'

const ControlPanel = ({ isDemoMode = false, setIsDemoMode = () => {} }) => {
  const [recognitionMode, setRecognitionMode] = useState('browser')
  const [availableModes, setAvailableModes] = useState([])
  const [showLocalConfig, setShowLocalConfig] = useState(false)
  const {
    isRecording,
    isTranslationEnabled,
    currentLanguage,
    targetLanguage,
    subtitles,
    clearSubtitles,
    toggleTranslation,
    translateAllSubtitles,
    exportSubtitles,
    setCurrentLanguage,
    setTargetLanguage
  } = useSubtitleStore()

  // 初始化可用的识别模式
  useEffect(() => {
    const modes = speechRecognitionService.getAvailableModes()
    setAvailableModes(modes)

    // 设置默认模式为第一个可用模式
    if (modes.length > 0) {
      setRecognitionMode(modes[0].id)
      speechRecognitionService.setRecognitionMode(modes[0].id)
    }
  }, [])

  // 处理识别模式变更
  const handleRecognitionModeChange = (mode) => {
    setRecognitionMode(mode)
    speechRecognitionService.setRecognitionMode(mode)
  }

  // 获取模式图标
  const getModeIcon = (mode) => {
    switch (mode) {
      case 'browser':
        return <Monitor size={16} />
      case 'offline':
        return <Cpu size={16} />
      case 'cloud':
        return <Cloud size={16} />
      case 'local':
        return <Server size={16} />
      default:
        return <Mic size={16} />
    }
  }

  // 处理局域网配置变更
  const handleLocalConfigChange = (config) => {
    console.log('局域网配置变更:', config)

    // 更新可用模式
    const modes = speechRecognitionService.getAvailableModes()
    setAvailableModes(modes)

    if (config.connected) {
      setShowLocalConfig(false)
      // 如果连接成功，自动切换到局域网模式
      setRecognitionMode('local')
      speechRecognitionService.setRecognitionMode('local')
    }
  }



  // 识别语言选项
  const languages = [
    { code: 'auto', name: '自动检测' },
    { code: 'zh-CN', name: '中文' },
    { code: 'en-US', name: 'English' },
    { code: 'ja-JP', name: '日本語' },
    { code: 'ko-KR', name: '한국어' },
    { code: 'es-ES', name: 'Español' },
    { code: 'fr-FR', name: 'Français' },
    { code: 'de-DE', name: 'Deutsch' },
    { code: 'ru-RU', name: 'Русский' }
  ]

  const targetLanguages = [
    { code: 'en', name: 'English' },
    { code: 'zh-CN', name: '中文' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'ru', name: 'Русский' }
  ]

  // 导出字幕文件
  const handleExport = (format) => {
    if (subtitles.length === 0) {
      alert('没有字幕可以导出')
      return
    }

    const content = exportSubtitles(format)
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const filename = `subtitles.${format}`
    saveAs(blob, filename)
  }

  // 清空所有字幕
  const handleClearAll = () => {
    if (subtitles.length === 0) return
    
    if (window.confirm('确定要清空所有字幕吗？此操作不可撤销。')) {
      clearSubtitles()
    }
  }

  // 批量翻译
  const handleTranslateAll = async () => {
    if (subtitles.length === 0) {
      alert('没有字幕可以翻译')
      return
    }

    if (!isTranslationEnabled) {
      alert('请先启用翻译功能')
      return
    }

    try {
      await translateAllSubtitles()
      alert('翻译完成！')
    } catch (error) {
      alert('翻译失败，请稍后重试')
    }
  }

  return (
    <div className="space-y-6">
      {/* 录制状态 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          {isRecording ? <Mic className="mr-2 text-red-500" /> : <MicOff className="mr-2 text-gray-400" />}
          录制状态
        </h3>
        
        <div className="text-center">
          {isRecording ? (
            <div className="text-red-600">
              <div className="animate-pulse text-2xl mb-2">🔴</div>
              <p className="font-medium">正在录制...</p>
              <p className="text-sm text-gray-600">语音识别进行中</p>
            </div>
          ) : (
            <div className="text-gray-500">
              <div className="text-2xl mb-2">⏸️</div>
              <p>未在录制</p>
              <p className="text-sm">播放视频开始录制</p>
            </div>
          )}
        </div>
      </div>

      {/* 语言设置 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Settings className="mr-2" />
          语言设置
        </h3>
        
        <div className="space-y-4">
          {/* 模式选择 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">
                演示模式
              </label>
              <button
                onClick={() => setIsDemoMode && setIsDemoMode(!isDemoMode)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  isDemoMode ? 'bg-gray-300' : 'bg-blue-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isDemoMode ? 'translate-x-1' : 'translate-x-6'
                  }`}
                />
              </button>
            </div>
            <p className="text-xs text-gray-500">
              {isDemoMode
                ? '当前为演示模式，会自动生成示例字幕'
                : '当前为语音识别模式，需要麦克风权限'
              }
            </p>
          </div>

          {/* 识别模式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              识别模式
            </label>
            <div className="space-y-2">
              {availableModes.map((mode) => (
                <label key={mode.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name="recognitionMode"
                    value={mode.id}
                    checked={recognitionMode === mode.id}
                    onChange={(e) => handleRecognitionModeChange(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-2">
                      {getModeIcon(mode.id)}
                      <div>
                        <div className="font-medium text-gray-900">{mode.name}</div>
                        <div className="text-sm text-gray-500">{mode.description}</div>
                        {!mode.available && mode.id === 'local' && (
                          <div className="text-xs text-orange-500">需要配置</div>
                        )}
                        {!mode.available && mode.id !== 'local' && (
                          <div className="text-xs text-red-500">不可用</div>
                        )}
                      </div>
                    </div>

                    {mode.id === 'local' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowLocalConfig(true)
                        }}
                        className="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded border border-blue-300 hover:border-blue-500"
                      >
                        配置
                      </button>
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 识别语言 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              识别语言
            </label>
            <select
              value={currentLanguage}
              onChange={(e) => setCurrentLanguage(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {languages.map((lang) => (
                <option key={lang.code} value={lang.code}>
                  {lang.name}
                </option>
              ))}
            </select>
          </div>

          {/* 翻译设置 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">
                启用翻译
              </label>
              <button
                onClick={toggleTranslation}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  isTranslationEnabled ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isTranslationEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            {isTranslationEnabled && (
              <select
                value={targetLanguage}
                onChange={(e) => setTargetLanguage(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {targetLanguages.map((lang) => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>
      </div>

      {/* 字幕统计 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">字幕统计</h3>
        
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-blue-600">{subtitles.length}</div>
            <div className="text-sm text-gray-600">总字幕数</div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-3">
            <div className="text-2xl font-bold text-green-600">
              {subtitles.filter(sub => sub.translatedText).length}
            </div>
            <div className="text-sm text-gray-600">已翻译</div>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">操作</h3>
        
        <div className="space-y-3">
          {/* 翻译按钮 */}
          {isTranslationEnabled && (
            <button
              onClick={handleTranslateAll}
              disabled={subtitles.length === 0}
              className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              <Globe className="mr-2" size={16} />
              批量翻译
            </button>
          )}



          {/* 导出按钮 */}
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => handleExport('srt')}
              disabled={subtitles.length === 0}
              className="flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
            >
              <Download className="mr-1" size={14} />
              导出SRT
            </button>

            <button
              onClick={() => handleExport('vtt')}
              disabled={subtitles.length === 0}
              className="flex items-center justify-center px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors text-sm"
            >
              <Download className="mr-1" size={14} />
              导出VTT
            </button>
          </div>

          {/* 清空按钮 */}
          <button
            onClick={handleClearAll}
            disabled={subtitles.length === 0}
            className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            <Trash2 className="mr-2" size={16} />
            清空字幕
          </button>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h4 className="font-medium text-blue-800 mb-3">使用说明</h4>

        <div className="space-y-3">
          <div>
            <h5 className="font-medium text-blue-700 mb-1">演示模式</h5>
            <ul className="text-sm text-blue-600 space-y-1 ml-2">
              <li>• 上传视频文件并播放查看演示效果</li>
              <li>• 自动生成示例字幕展示功能</li>
              <li>• 适合了解系统功能和界面</li>
            </ul>
          </div>

          <div>
            <h5 className="font-medium text-blue-700 mb-1">语音识别模式</h5>
            <ul className="text-sm text-blue-600 space-y-1 ml-2">
              <li>• 需要麦克风权限进行实时语音识别</li>
              <li>• 支持Chrome和Edge浏览器</li>
              <li>• 播放视频时对着麦克风说话生成字幕</li>
              <li>• 适合为无声视频添加解说字幕</li>
            </ul>
          </div>

          <div>
            <h5 className="font-medium text-blue-700 mb-1">其他功能</h5>
            <ul className="text-sm text-blue-600 space-y-1 ml-2">
              <li>• 可以编辑和删除生成的字幕</li>
              <li>• 启用翻译功能可实时翻译字幕</li>
              <li>• 支持导出SRT和VTT格式字幕文件</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 技术说明 */}
      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <h4 className="font-medium text-gray-800 mb-3">技术解决方案</h4>
        <div className="text-sm text-gray-600 space-y-3">
          <div>
            <h5 className="font-medium text-gray-700 mb-1">🖥️ 浏览器模式</h5>
            <p>使用浏览器内置语音识别API，支持麦克风实时输入。适合为无声视频添加解说字幕。</p>
          </div>

          <div>
            <h5 className="font-medium text-gray-700 mb-1">🎵 局域网模式 (推荐)</h5>
            <p>使用FFmpeg.wasm提取视频音频，发送到局域网Whisper服务进行识别。支持直接处理视频音频，识别准确度高。</p>
          </div>

          <div>
            <h5 className="font-medium text-gray-700 mb-1">☁️ 云端模式</h5>
            <p>集成云端语音识别API（Google、Azure、OpenAI等），识别准确度最高。需要API密钥和网络连接。</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded p-3">
            <h5 className="font-medium text-green-800 mb-1">✅ 技术突破</h5>
            <p className="text-green-700">
              现已实现使用FFmpeg.wasm在浏览器中直接提取视频音频，结合局域网Whisper服务实现完整的视频字幕生成流程。
            </p>
          </div>

          <div>
            <h5 className="font-medium text-gray-700 mb-1">🔧 完整工作流程</h5>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>上传视频文件到浏览器</li>
              <li>使用FFmpeg.wasm提取音频（WAV格式，16kHz）</li>
              <li>发送音频到局域网Whisper服务识别</li>
              <li>实时显示识别结果和字幕</li>
              <li>支持编辑、翻译和导出字幕</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 局域网Whisper配置 */}
      {showLocalConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-xl font-semibold">局域网Whisper服务配置</h2>
              <button
                onClick={() => setShowLocalConfig(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="p-4">
              <LocalWhisperConfig onConfigChange={handleLocalConfigChange} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ControlPanel
