import React, { useEffect, useState } from 'react'
import { useSubtitleStore } from '../../stores/subtitleStore'

const SubtitleDisplay = () => {
  const { 
    getCurrentSubtitle, 
    currentVideoTime, 
    isTranslationEnabled,
    subtitles 
  } = useSubtitleStore()
  
  const [currentSubtitle, setCurrentSubtitle] = useState(null)

  // 监听视频时间变化，更新当前字幕
  useEffect(() => {
    const subtitle = getCurrentSubtitle()
    setCurrentSubtitle(subtitle)
  }, [currentVideoTime, subtitles, getCurrentSubtitle])

  return (
    <div className="mt-4">
      {/* 实时字幕显示区域 */}
      <div className="bg-black bg-opacity-80 rounded-lg p-4 min-h-[100px] flex items-center justify-center">
        {currentSubtitle ? (
          <div className="text-center">
            {/* 原文字幕 */}
            <div className="text-white text-lg md:text-xl font-medium leading-relaxed mb-2">
              {currentSubtitle.text}
            </div>
            
            {/* 翻译字幕 */}
            {isTranslationEnabled && currentSubtitle.translatedText && (
              <div className="text-yellow-300 text-base md:text-lg leading-relaxed">
                {currentSubtitle.translatedText}
              </div>
            )}
          </div>
        ) : (
          <div className="text-gray-400 text-center">
            <div className="text-4xl mb-2">💬</div>
            <p>字幕将在这里显示</p>
            <p className="text-sm mt-1">开始播放视频以生成实时字幕</p>
          </div>
        )}
      </div>

      {/* 字幕历史列表 */}
      {subtitles.length > 0 && (
        <div className="mt-4 bg-white rounded-lg shadow-lg">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">字幕历史</h3>
            <p className="text-sm text-gray-600">共 {subtitles.length} 条字幕</p>
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {subtitles.map((subtitle) => (
              <SubtitleItem 
                key={subtitle.id} 
                subtitle={subtitle}
                isCurrent={currentSubtitle?.id === subtitle.id}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// 单个字幕项组件
const SubtitleItem = ({ subtitle, isCurrent }) => {
  const { 
    updateSubtitle, 
    removeSubtitle, 
    translateSubtitle,
    isTranslationEnabled 
  } = useSubtitleStore()
  
  const [isEditing, setIsEditing] = useState(false)
  const [editText, setEditText] = useState(subtitle.text)

  // 格式化时间显示
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  // 保存编辑
  const handleSave = () => {
    updateSubtitle(subtitle.id, { text: editText })
    setIsEditing(false)
  }

  // 取消编辑
  const handleCancel = () => {
    setEditText(subtitle.text)
    setIsEditing(false)
  }

  // 翻译字幕
  const handleTranslate = () => {
    translateSubtitle(subtitle.id)
  }

  return (
    <div className={`p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors ${
      isCurrent ? 'bg-blue-50 border-blue-200' : ''
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {/* 时间戳 */}
          <div className="text-xs text-gray-500 mb-2">
            {formatTime(subtitle.startTime)} - {formatTime(subtitle.endTime)}
            {isCurrent && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                当前播放
              </span>
            )}
          </div>

          {/* 字幕文本 */}
          {isEditing ? (
            <div className="space-y-2">
              <textarea
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md resize-none"
                rows="2"
                autoFocus
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  保存
                </button>
                <button
                  onClick={handleCancel}
                  className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-400"
                >
                  取消
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="text-gray-800 mb-1">{subtitle.text}</div>
              {isTranslationEnabled && subtitle.translatedText && (
                <div className="text-blue-600 text-sm italic">
                  {subtitle.translatedText}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        {!isEditing && (
          <div className="flex space-x-1 ml-4">
            <button
              onClick={() => setIsEditing(true)}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="编辑"
            >
              ✏️
            </button>
            
            {isTranslationEnabled && !subtitle.translatedText && (
              <button
                onClick={handleTranslate}
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                title="翻译"
              >
                🌐
              </button>
            )}
            
            <button
              onClick={() => removeSubtitle(subtitle.id)}
              className="p-1 text-gray-400 hover:text-red-600 transition-colors"
              title="删除"
            >
              🗑️
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default SubtitleDisplay
