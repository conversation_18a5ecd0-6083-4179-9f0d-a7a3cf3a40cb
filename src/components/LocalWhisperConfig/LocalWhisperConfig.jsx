import React, { useState, useEffect } from 'react'
import { Search, Server, CheckCircle, XCircle, Settings, Wifi, WifiOff } from 'lucide-react'
import speechRecognitionService from '../../services/speechRecognitionService'
import localWhisperService from '../../services/localWhisperService'

const LocalWhisperConfig = ({ onConfigChange = () => {} }) => {
  const [serverUrl, setServerUrl] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [isDiscovering, setIsDiscovering] = useState(false)
  const [serverInfo, setServerInfo] = useState(null)
  const [connectionStatus, setConnectionStatus] = useState('')
  const [discoveredServices, setDiscoveredServices] = useState([])
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 检查连接状态
  useEffect(() => {
    checkConnectionStatus()
  }, [])

  const checkConnectionStatus = async () => {
    try {
      const status = await speechRecognitionService.getLocalWhisperStatus()
      setIsConnected(status.connected)
      setServerInfo(status.serverInfo)
      setConnectionStatus(status.connected ? '已连接' : status.error || '未连接')

      if (status.connected && localWhisperService.serverUrl) {
        setServerUrl(localWhisperService.serverUrl)
      }
    } catch (error) {
      setIsConnected(false)
      setConnectionStatus('检查状态失败')
    }
  }

  const handleConnect = async () => {
    if (!serverUrl.trim()) {
      setConnectionStatus('请输入服务器地址')
      return
    }

    try {
      setConnectionStatus('正在连接...')
      await speechRecognitionService.setLocalWhisperServer(serverUrl.trim())

      setIsConnected(true)
      setConnectionStatus('连接成功')

      // 获取服务器信息
      const info = localWhisperService.getServerInfo()
      setServerInfo(info)

      onConfigChange({ connected: true, serverUrl: serverUrl.trim(), serverInfo: info })

    } catch (error) {
      setIsConnected(false)
      setConnectionStatus(`连接失败: ${error.message}`)
      onConfigChange({ connected: false, error: error.message })
    }
  }

  const handleDisconnect = () => {
    localWhisperService.disconnect()
    setIsConnected(false)
    setServerInfo(null)
    setConnectionStatus('已断开连接')
    onConfigChange({ connected: false })
  }

  const handleDiscover = async () => {
    setIsDiscovering(true)
    setConnectionStatus('正在搜索局域网服务...')
    setDiscoveredServices([])

    try {
      const service = await speechRecognitionService.discoverLocalWhisperService()

      setDiscoveredServices([service])
      setServerUrl(service.url)
      setIsConnected(true)
      setServerInfo(service.info)
      setConnectionStatus('自动发现并连接成功')

      onConfigChange({
        connected: true,
        serverUrl: service.url,
        serverInfo: service.info,
        discovered: true
      })

    } catch (error) {
      setConnectionStatus(`未发现可用服务: ${error.message}`)
      setDiscoveredServices([])
    } finally {
      setIsDiscovering(false)
    }
  }

  const handleTestConnection = async () => {
    if (!serverUrl.trim()) {
      setConnectionStatus('请输入服务器地址')
      return
    }

    try {
      setConnectionStatus('正在测试连接...')

      // 直接测试连接
      const testUrl = serverUrl.startsWith('http') ? `${serverUrl}/health` : `http://${serverUrl}/health`
      console.log('测试连接到:', testUrl)

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })

      console.log('响应状态:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('响应数据:', data)
        setConnectionStatus('连接测试成功')
      } else {
        setConnectionStatus(`连接测试失败: HTTP ${response.status}`)
      }
    } catch (error) {
      console.error('测试连接错误:', error)
      setConnectionStatus(`测试失败: ${error.message}`)
    }
  }



  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <Server className="mr-2" />
        局域网Whisper服务配置
      </h3>

      {/* 连接状态 */}
      <div className="mb-4 p-3 rounded-lg bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {isConnected ? (
              <CheckCircle className="text-green-500 mr-2" size={20} />
            ) : (
              <XCircle className="text-red-500 mr-2" size={20} />
            )}
            <span className="font-medium">
              状态: {connectionStatus}
            </span>
          </div>

          {isConnected && (
            <button
              onClick={handleDisconnect}
              className="text-sm text-red-600 hover:text-red-800"
            >
              断开连接
            </button>
          )}
        </div>

        {serverInfo && (
          <div className="mt-2 text-sm text-gray-600">
            <div>模型: {serverInfo.model_size}</div>
            <div>设备: {serverInfo.device}</div>
          </div>
        )}
      </div>

      {!isConnected && (
        <>
          {/* 自动发现 */}
          <div className="mb-4">
            <button
              onClick={handleDiscover}
              disabled={isDiscovering}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isDiscovering ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  搜索中...
                </>
              ) : (
                <>
                  <Search className="mr-2" size={16} />
                  自动发现服务
                </>
              )}
            </button>
          </div>

          {/* 发现的服务列表 */}
          {discoveredServices.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">发现的服务:</h4>
              {discoveredServices.map((service, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                  onClick={() => {
                    setServerUrl(service.url)
                    handleConnect()
                  }}
                >
                  <div className="font-medium">{service.url}</div>
                  <div className="text-sm text-gray-600">
                    模型: {service.info.model_size} | 设备: {service.info.device}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 手动配置 */}
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                服务器地址
              </label>
              <input
                type="text"
                value={serverUrl}
                onChange={(e) => setServerUrl(e.target.value)}
                placeholder="例: *************:8000 或 http://*************:8000"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleConnect()}
              />
            </div>

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={handleTestConnection}
                disabled={!serverUrl.trim()}
                className="flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Settings className="mr-2" size={16} />
                测试连接
              </button>

              <button
                onClick={handleConnect}
                disabled={!serverUrl.trim()}
                className="flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                <Wifi className="mr-2" size={16} />
                连接服务
              </button>
            </div>
          </div>
        </>
      )}

      {/* 高级设置 */}
      <div className="mt-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center text-sm text-gray-600 hover:text-gray-800"
        >
          <Settings className="mr-1" size={14} />
          高级设置
        </button>

        {showAdvanced && (
          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
            <div className="space-y-3 text-sm">
              <div>
                <h5 className="font-medium text-gray-700 mb-1">常用地址示例:</h5>
                <ul className="text-gray-600 space-y-1">
                  <li>• 本机: localhost:8000</li>
                  <li>• 局域网: *************:8000</li>
                  <li>• 带协议: http://*************:8000</li>
                </ul>
              </div>

              <div>
                <h5 className="font-medium text-gray-700 mb-1">故障排除:</h5>
                <ul className="text-gray-600 space-y-1">
                  <li>• 确保服务器已启动</li>
                  <li>• 检查防火墙设置</li>
                  <li>• 确认端口号正确</li>
                  <li>• 检查网络连接</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>



      {/* 使用说明 */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <h5 className="font-medium text-blue-800 mb-1">使用说明:</h5>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 首先在局域网设备上启动Whisper服务</li>
          <li>• 使用自动发现或手动输入服务器地址</li>
          <li>• 连接成功后即可使用局域网识别模式</li>
          <li>• 支持直接处理视频音频，准确度高</li>
        </ul>
      </div>
    </div>
  )
}

export default LocalWhisperConfig
