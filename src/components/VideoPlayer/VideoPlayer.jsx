import React, { useRef, useState, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX, SkipBack, SkipForward } from 'lucide-react'
import { useSubtitleStore } from '../../stores/subtitleStore'

const VideoPlayer = () => {
  const videoRef = useRef(null)
  const fileInputRef = useRef(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [videoFile, setVideoFile] = useState(null)
  
  const {
    setCurrentVideoTime,
    startRecording,
    stopRecording
  } = useSubtitleStore()

  // 处理视频文件上传
  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (file && file.type.startsWith('video/')) {
      const url = URL.createObjectURL(file)
      setVideoFile(url)
      setIsPlaying(false)
      setCurrentTime(0)
    }
  }

  // 播放/暂停控制
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        stopRecording()
      } else {
        videoRef.current.play()
        startRecording(videoRef.current)
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 时间更新
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime
      setCurrentTime(current)
      setCurrentVideoTime(current)
    }
  }

  // 视频加载完成
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration)
    }
  }

  // 进度条拖拽
  const handleSeek = (event) => {
    const rect = event.currentTarget.getBoundingClientRect()
    const percent = (event.clientX - rect.left) / rect.width
    const newTime = percent * duration
    
    if (videoRef.current) {
      videoRef.current.currentTime = newTime
      setCurrentTime(newTime)
      setCurrentVideoTime(newTime)
    }
  }

  // 音量控制
  const handleVolumeChange = (event) => {
    const newVolume = parseFloat(event.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
    setIsMuted(newVolume === 0)
  }

  // 静音切换
  const toggleMute = () => {
    if (videoRef.current) {
      if (isMuted) {
        videoRef.current.volume = volume
        setIsMuted(false)
      } else {
        videoRef.current.volume = 0
        setIsMuted(true)
      }
    }
  }

  // 快进/快退
  const skipTime = (seconds) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
      videoRef.current.currentTime = newTime
      setCurrentTime(newTime)
      setCurrentVideoTime(newTime)
    }
  }

  // 格式化时间显示
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 清理资源
  useEffect(() => {
    return () => {
      if (videoFile) {
        URL.revokeObjectURL(videoFile)
      }
    }
  }, [videoFile])

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* 视频区域 */}
      <div className="relative bg-black aspect-video">
        {videoFile ? (
          <video
            ref={videoRef}
            src={videoFile}
            className="w-full h-full object-contain"
            onTimeUpdate={handleTimeUpdate}
            onLoadedMetadata={handleLoadedMetadata}
            onEnded={() => {
              setIsPlaying(false)
              stopRecording()
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-white">
            <div className="text-center">
              <div className="text-6xl mb-4">📹</div>
              <p className="text-lg mb-4">请选择视频文件</p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                选择视频
              </button>
            </div>
          </div>
        )}
        
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileUpload}
          className="hidden"
        />
      </div>

      {/* 控制栏 */}
      {videoFile && (
        <div className="p-4 bg-gray-50">
          {/* 进度条 */}
          <div className="mb-4">
            <div
              className="w-full h-2 bg-gray-300 rounded-full cursor-pointer"
              onClick={handleSeek}
            >
              <div
                className="h-full bg-blue-600 rounded-full transition-all"
                style={{ width: `${(currentTime / duration) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-sm text-gray-600 mt-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>

          {/* 模式说明 */}
          <div className="mb-3 p-2 border rounded-md bg-green-50 border-green-200">
            <p className="text-sm text-green-700">
              🎤 <strong>语音识别模式</strong>：播放视频时系统会根据选择的识别模式进行字幕生成
            </p>
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => skipTime(-10)}
                className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                title="后退10秒"
              >
                <SkipBack size={20} />
              </button>
              
              <button
                onClick={togglePlay}
                className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors"
              >
                {isPlaying ? <Pause size={24} /> : <Play size={24} />}
              </button>
              
              <button
                onClick={() => skipTime(10)}
                className="p-2 hover:bg-gray-200 rounded-full transition-colors"
                title="前进10秒"
              >
                <SkipForward size={20} />
              </button>
            </div>

            {/* 音量控制 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleMute}
                className="p-2 hover:bg-gray-200 rounded-full transition-colors"
              >
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
              
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-20"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoPlayer
