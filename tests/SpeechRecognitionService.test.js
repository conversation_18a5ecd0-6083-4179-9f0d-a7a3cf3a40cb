import { describe, it, expect, beforeEach, vi } from 'vitest'
import speechRecognitionService from '../src/services/speechRecognitionService'

// Mock Web APIs
const mockSpeechRecognition = vi.fn()
mockSpeechRecognition.prototype.start = vi.fn()
mockSpeechRecognition.prototype.stop = vi.fn()

const mockMediaDevices = {
  getUserMedia: vi.fn(() => Promise.resolve({}))
}

const mockAudioContext = vi.fn()
mockAudioContext.prototype.createMediaElementSource = vi.fn()
mockAudioContext.prototype.createAnalyser = vi.fn()
mockAudioContext.prototype.createMediaStreamDestination = vi.fn()
mockAudioContext.prototype.close = vi.fn()

const mockMediaRecorder = vi.fn()
mockMediaRecorder.prototype.start = vi.fn()
mockMediaRecorder.prototype.stop = vi.fn()

// Setup global mocks
global.webkitSpeechRecognition = mockSpeechRecognition
global.navigator = {
  mediaDevices: mockMediaDevices
}
global.AudioContext = mockAudioContext
global.webkitAudioContext = mockAudioContext
global.MediaRecorder = mockMediaRecorder

describe('SpeechRecognitionService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    speechRecognitionService.stopRecognition()
  })

  describe('浏览器支持检测', () => {
    it('应该正确检测语音识别支持', () => {
      expect(speechRecognitionService.isRecognitionSupported()).toBe(true)
    })

    it('应该返回支持的语言列表', () => {
      const languages = speechRecognitionService.getSupportedLanguages()
      
      expect(Array.isArray(languages)).toBe(true)
      expect(languages.length).toBeGreaterThan(0)
      expect(languages[0]).toHaveProperty('code')
      expect(languages[0]).toHaveProperty('name')
      
      // 检查是否包含常用语言
      const languageCodes = languages.map(lang => lang.code)
      expect(languageCodes).toContain('zh-CN')
      expect(languageCodes).toContain('en-US')
      expect(languageCodes).toContain('ja-JP')
    })
  })

  describe('麦克风语音识别', () => {
    it('应该能够启动麦克风识别', async () => {
      const options = {
        language: 'zh-CN',
        onResult: vi.fn(),
        onError: vi.fn(),
        onEnd: vi.fn()
      }

      await speechRecognitionService.startMicrophoneRecognition(options)

      expect(mockMediaDevices.getUserMedia).toHaveBeenCalledWith({ audio: true })
      expect(mockSpeechRecognition).toHaveBeenCalled()
    })

    it('应该正确配置语音识别参数', async () => {
      const options = {
        language: 'en-US',
        onResult: vi.fn(),
        onError: vi.fn(),
        onEnd: vi.fn()
      }

      await speechRecognitionService.startMicrophoneRecognition(options)

      const recognitionInstance = mockSpeechRecognition.mock.instances[0]
      expect(recognitionInstance.continuous).toBe(true)
      expect(recognitionInstance.interimResults).toBe(true)
      expect(recognitionInstance.lang).toBe('en-US')
    })

    it('应该处理麦克风权限被拒绝的情况', async () => {
      mockMediaDevices.getUserMedia.mockRejectedValueOnce(new Error('Permission denied'))

      const options = {
        language: 'zh-CN',
        onResult: vi.fn(),
        onError: vi.fn(),
        onEnd: vi.fn()
      }

      await expect(speechRecognitionService.startMicrophoneRecognition(options))
        .rejects.toThrow('Permission denied')
    })

    it('应该在不支持语音识别时抛出错误', async () => {
      // 临时禁用语音识别支持
      const originalIsSupported = speechRecognitionService.isRecognitionSupported
      speechRecognitionService.isRecognitionSupported = () => false

      const options = {
        language: 'zh-CN',
        onResult: vi.fn(),
        onError: vi.fn(),
        onEnd: vi.fn()
      }

      await expect(speechRecognitionService.startMicrophoneRecognition(options))
        .rejects.toThrow('浏览器不支持语音识别功能')

      // 恢复原始方法
      speechRecognitionService.isRecognitionSupported = originalIsSupported
    })
  })

  describe('视频音频捕获', () => {
    let mockVideoElement

    beforeEach(() => {
      mockVideoElement = {
        tagName: 'VIDEO'
      }

      // Mock audio context methods
      const mockSource = { connect: vi.fn() }
      const mockAnalyser = { 
        fftSize: 0,
        frequencyBinCount: 1024,
        getByteFrequencyData: vi.fn()
      }
      const mockDestination = { 
        stream: { 
          getTracks: vi.fn(() => [])
        }
      }

      mockAudioContext.prototype.createMediaElementSource.mockReturnValue(mockSource)
      mockAudioContext.prototype.createAnalyser.mockReturnValue(mockAnalyser)
      mockAudioContext.prototype.createMediaStreamDestination.mockReturnValue(mockDestination)
    })

    it('应该能够尝试捕获视频音频', async () => {
      const options = {
        onAudioData: vi.fn(),
        onError: vi.fn()
      }

      const result = await speechRecognitionService.tryVideoAudioCapture(mockVideoElement, options)

      expect(result).toBe(true)
      expect(mockAudioContext).toHaveBeenCalled()
    })

    it('应该处理音频捕获失败的情况', async () => {
      mockAudioContext.mockImplementationOnce(() => {
        throw new Error('AudioContext creation failed')
      })

      const options = {
        onAudioData: vi.fn(),
        onError: vi.fn()
      }

      const result = await speechRecognitionService.tryVideoAudioCapture(mockVideoElement, options)

      expect(result).toBe(false)
      expect(options.onError).toHaveBeenCalled()
    })
  })

  describe('停止识别', () => {
    it('应该能够停止所有识别活动', () => {
      // 模拟正在进行的识别
      speechRecognitionService.recognition = {
        stop: vi.fn()
      }
      speechRecognitionService.mediaRecorder = {
        state: 'recording',
        stop: vi.fn()
      }
      speechRecognitionService.audioContext = {
        state: 'running',
        close: vi.fn()
      }

      speechRecognitionService.stopRecognition()

      expect(speechRecognitionService.recognition.stop).toHaveBeenCalled()
      expect(speechRecognitionService.mediaRecorder.stop).toHaveBeenCalled()
      expect(speechRecognitionService.audioContext.close).toHaveBeenCalled()
    })

    it('应该安全处理空值', () => {
      speechRecognitionService.recognition = null
      speechRecognitionService.mediaRecorder = null
      speechRecognitionService.audioContext = null

      // 不应该抛出错误
      expect(() => speechRecognitionService.stopRecognition()).not.toThrow()
    })
  })


})
