import '@testing-library/jest-dom'

// Mock Web APIs that are not available in jsdom
Object.defineProperty(window, 'SpeechRecognition', {
  writable: true,
  value: class MockSpeechRecognition {
    constructor() {
      this.continuous = false
      this.interimResults = false
      this.lang = 'en-US'
      this.onresult = null
      this.onerror = null
      this.onend = null
    }
    
    start() {
      // Mock implementation
    }
    
    stop() {
      // Mock implementation
    }
  }
})

Object.defineProperty(window, 'webkitSpeechRecognition', {
  writable: true,
  value: window.SpeechRecognition
})

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-url')
global.URL.revokeObjectURL = vi.fn()
