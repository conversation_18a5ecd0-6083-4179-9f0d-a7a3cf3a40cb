import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useSubtitleStore } from '../src/stores/subtitleStore'

// Mock speech recognition service
vi.mock('../src/services/speechRecognitionService', () => ({
  default: {
    isRecognitionSupported: vi.fn(() => true),
    startMicrophoneRecognition: vi.fn(() => Promise.resolve()),
    stopRecognition: vi.fn(),
    tryVideoAudioCapture: vi.fn(() => Promise.resolve(true))
  }
}))

describe('SubtitleStore', () => {
  let store

  beforeEach(() => {
    // 重置store状态
    useSubtitleStore.setState({
      subtitles: [],
      currentVideoTime: 0,
      isRecording: false,
      currentLanguage: 'auto',
      targetLanguage: 'en',
      isTranslationEnabled: false
    })
    store = useSubtitleStore.getState()
  })

  describe('基本状态管理', () => {
    it('应该有正确的初始状态', () => {
      expect(store.subtitles).toEqual([])
      expect(store.currentVideoTime).toBe(0)
      expect(store.isRecording).toBe(false)
      expect(store.currentLanguage).toBe('auto')
      expect(store.targetLanguage).toBe('en')
      expect(store.isTranslationEnabled).toBe(false)
    })

    it('应该能够设置当前视频时间', () => {
      store.setCurrentVideoTime(10.5)
      const updatedStore = useSubtitleStore.getState()
      expect(updatedStore.currentVideoTime).toBe(10.5)
    })

    it('应该能够切换翻译功能', () => {
      expect(store.isTranslationEnabled).toBe(false)
      store.toggleTranslation()
      const updatedStore = useSubtitleStore.getState()
      expect(updatedStore.isTranslationEnabled).toBe(true)
    })

    it('应该能够设置语言', () => {
      store.setCurrentLanguage('en-US')
      let updatedStore = useSubtitleStore.getState()
      expect(updatedStore.currentLanguage).toBe('en-US')

      store.setTargetLanguage('zh-CN')
      updatedStore = useSubtitleStore.getState()
      expect(updatedStore.targetLanguage).toBe('zh-CN')
    })
  })

  describe('字幕管理', () => {
    it('应该能够添加字幕', () => {
      const subtitle = {
        startTime: 5,
        endTime: 8,
        text: '测试字幕'
      }

      store.addSubtitle(subtitle)
      const updatedStore = useSubtitleStore.getState()

      expect(updatedStore.subtitles).toHaveLength(1)
      expect(updatedStore.subtitles[0]).toMatchObject({
        startTime: 5,
        endTime: 8,
        text: '测试字幕',
        translatedText: ''
      })
      expect(updatedStore.subtitles[0].id).toBeDefined()
    })

    it('应该为没有结束时间的字幕设置默认持续时间', () => {
      const subtitle = {
        startTime: 5,
        text: '测试字幕'
      }

      store.addSubtitle(subtitle)
      const updatedStore = useSubtitleStore.getState()

      expect(updatedStore.subtitles[0].endTime).toBe(8) // startTime + 3
    })

    it('应该能够更新字幕', () => {
      const subtitle = {
        startTime: 5,
        endTime: 8,
        text: '原始字幕'
      }

      store.addSubtitle(subtitle)
      let updatedStore = useSubtitleStore.getState()
      const subtitleId = updatedStore.subtitles[0].id

      store.updateSubtitle(subtitleId, {
        text: '更新后的字幕',
        translatedText: '翻译文本'
      })

      updatedStore = useSubtitleStore.getState()
      expect(updatedStore.subtitles[0].text).toBe('更新后的字幕')
      expect(updatedStore.subtitles[0].translatedText).toBe('翻译文本')
    })

    it('应该能够删除字幕', () => {
      const subtitle = {
        startTime: 5,
        endTime: 8,
        text: '测试字幕'
      }

      store.addSubtitle(subtitle)
      let updatedStore = useSubtitleStore.getState()
      const subtitleId = updatedStore.subtitles[0].id

      expect(updatedStore.subtitles).toHaveLength(1)

      store.removeSubtitle(subtitleId)
      updatedStore = useSubtitleStore.getState()

      expect(updatedStore.subtitles).toHaveLength(0)
    })

    it('应该能够清空所有字幕', () => {
      store.addSubtitle({ startTime: 1, text: '字幕1' })
      store.addSubtitle({ startTime: 2, text: '字幕2' })

      let updatedStore = useSubtitleStore.getState()
      expect(updatedStore.subtitles).toHaveLength(2)

      store.clearSubtitles()
      updatedStore = useSubtitleStore.getState()

      expect(updatedStore.subtitles).toHaveLength(0)
    })
  })

  describe('当前字幕获取', () => {
    beforeEach(() => {
      // 添加测试字幕
      store.addSubtitle({ startTime: 5, endTime: 8, text: '字幕1' })
      store.addSubtitle({ startTime: 10, endTime: 13, text: '字幕2' })
      store.addSubtitle({ startTime: 15, endTime: 18, text: '字幕3' })
    })

    it('应该返回当前时间对应的字幕', () => {
      store.setCurrentVideoTime(6)
      const currentSubtitle = store.getCurrentSubtitle()
      expect(currentSubtitle.text).toBe('字幕1')

      store.setCurrentVideoTime(11)
      const currentSubtitle2 = store.getCurrentSubtitle()
      expect(currentSubtitle2.text).toBe('字幕2')
    })

    it('当没有匹配的字幕时应该返回undefined', () => {
      store.setCurrentVideoTime(20)
      const currentSubtitle = store.getCurrentSubtitle()
      expect(currentSubtitle).toBeUndefined()
    })

    it('应该正确处理边界时间', () => {
      store.setCurrentVideoTime(5) // 开始时间
      expect(store.getCurrentSubtitle().text).toBe('字幕1')

      store.setCurrentVideoTime(8) // 结束时间
      expect(store.getCurrentSubtitle().text).toBe('字幕1')

      store.setCurrentVideoTime(8.1) // 超出结束时间
      expect(store.getCurrentSubtitle()).toBeUndefined()
    })
  })



  describe('字幕导出', () => {
    beforeEach(() => {
      store.addSubtitle({
        startTime: 5,
        endTime: 8,
        text: '第一条字幕'
      })
      store.addSubtitle({
        startTime: 10,
        endTime: 13,
        text: '第二条字幕',
        translatedText: 'Second subtitle'
      })
    })

    it('应该能够导出SRT格式', () => {
      const srtContent = store.exportSubtitles('srt')
      
      expect(srtContent).toContain('1')
      expect(srtContent).toContain('00:00:05,000 --> 00:00:08,000')
      expect(srtContent).toContain('第一条字幕')
      expect(srtContent).toContain('2')
      expect(srtContent).toContain('00:00:10,000 --> 00:00:13,000')
      expect(srtContent).toContain('Second subtitle') // 应该使用翻译文本
    })

    it('应该能够导出VTT格式', () => {
      const vttContent = store.exportSubtitles('vtt')
      
      expect(vttContent).toContain('WEBVTT')
      expect(vttContent).toContain('00:00:05.000 --> 00:00:08.000')
      expect(vttContent).toContain('第一条字幕')
    })

    it('没有翻译时应该使用原文', () => {
      const srtContent = store.exportSubtitles('srt')
      expect(srtContent).toContain('第一条字幕')
    })
  })
})
