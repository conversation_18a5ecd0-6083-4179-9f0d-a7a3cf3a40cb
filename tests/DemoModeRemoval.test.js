import { describe, it, expect } from 'vitest'
import { useSubtitleStore } from '../src/stores/subtitleStore'
import speechRecognitionService from '../src/services/speechRecognitionService'

describe('演示模式移除验证', () => {
  it('SubtitleStore不应该包含演示模式相关的方法', () => {
    const store = useSubtitleStore.getState()

    // 检查演示模式相关的方法是否已被移除
    expect(store.startDemoMode).toBeUndefined()
    expect(store.stopDemoMode).toBeUndefined()
  })

  it('SpeechRecognitionService不应该包含演示字幕生成方法', () => {
    // 检查演示字幕生成方法是否已被移除
    expect(speechRecognitionService.generateDemoSubtitles).toBeUndefined()
  })

  it('SubtitleStore应该包含正确的语音识别相关方法', () => {
    const store = useSubtitleStore.getState()

    // 检查语音识别相关的方法是否存在
    expect(store.startRecording).toBeDefined()
    expect(store.stopRecording).toBeDefined()
    expect(store.addSubtitle).toBeDefined()
    expect(store.clearSubtitles).toBeDefined()
  })

  it('SpeechRecognitionService应该包含正确的识别方法', () => {
    // 检查语音识别相关的方法是否存在
    expect(speechRecognitionService.isRecognitionSupported).toBeDefined()
    expect(speechRecognitionService.startMicrophoneRecognition).toBeDefined()
    expect(speechRecognitionService.stopRecognition).toBeDefined()
    expect(speechRecognitionService.getAvailableModes).toBeDefined()
  })
})
