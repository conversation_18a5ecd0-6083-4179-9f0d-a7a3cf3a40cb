import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import VideoPlayer from '../src/components/VideoPlayer/VideoPlayer'

// Mock zustand store
vi.mock('../src/stores/subtitleStore', () => ({
  useSubtitleStore: () => ({
    setCurrentVideoTime: vi.fn(),
    startRecording: vi.fn(),
    stopRecording: vi.fn()
  })
}))

describe('VideoPlayer Component', () => {
  beforeEach(() => {
    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn(() => 'mock-video-url')
    global.URL.revokeObjectURL = vi.fn()
  })

  it('renders video upload interface initially', () => {
    render(<VideoPlayer />)
    
    expect(screen.getByText('请选择视频文件')).toBeInTheDocument()
    expect(screen.getByText('选择视频')).toBeInTheDocument()
  })

  it('shows video controls after file upload', () => {
    render(<VideoPlayer />)
    
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    
    fireEvent.change(fileInput, { target: { files: [mockFile] } })
    
    // Should show video element (though it won't actually play in test)
    expect(document.querySelector('video')).toBeInTheDocument()
  })

  it('handles play/pause button clicks', () => {
    render(<VideoPlayer />)

    // Upload a file first
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    // Find play button by its SVG content (since it doesn't have accessible text)
    const playButton = document.querySelector('button svg[class*="lucide-play"]').parentElement
    expect(playButton).toBeInTheDocument()

    // Click the play button
    fireEvent.click(playButton)
  })

  it('handles volume control', () => {
    render(<VideoPlayer />)

    // Upload a file first to show controls
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    const volumeSlider = screen.getByRole('slider')
    expect(volumeSlider).toBeInTheDocument()

    // 测试音量调节
    fireEvent.change(volumeSlider, { target: { value: '0.5' } })
    expect(volumeSlider.value).toBe('0.5')
  })

  it('shows volume percentage when dragging volume slider', () => {
    render(<VideoPlayer />)

    // Upload a file first to show controls
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    const volumeSlider = screen.getByRole('slider')

    // 模拟开始拖动
    fireEvent.mouseDown(volumeSlider)

    // 应该显示音量百分比
    expect(screen.getByText('100%')).toBeInTheDocument()

    // 改变音量值
    fireEvent.change(volumeSlider, { target: { value: '0.7' } })
    expect(screen.getByText('70%')).toBeInTheDocument()

    // 模拟结束拖动
    fireEvent.mouseUp(volumeSlider)

    // 音量显示应该仍然可见（在延迟隐藏之前）
    expect(screen.getByText('70%')).toBeInTheDocument()
  })

  it('handles keyboard shortcuts for video control', () => {
    render(<VideoPlayer />)

    // Upload a file first to enable keyboard controls
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    // Mock video element properties
    const videoElement = document.querySelector('video')
    Object.defineProperty(videoElement, 'currentTime', {
      writable: true,
      value: 10
    })
    Object.defineProperty(videoElement, 'duration', {
      writable: true,
      value: 100
    })

    // Test left arrow key (backward 5 seconds)
    fireEvent.keyDown(document, { key: 'ArrowLeft' })
    // Note: In real implementation, currentTime would be updated

    // Test right arrow key (forward 5 seconds)
    fireEvent.keyDown(document, { key: 'ArrowRight' })

    // Test spacebar (play/pause)
    fireEvent.keyDown(document, { key: ' ' })
  })

  it('shows keyboard shortcuts information', () => {
    render(<VideoPlayer />)

    // Upload a file first to show controls
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    // Should show keyboard shortcuts information
    expect(screen.getByText(/键盘快捷键/)).toBeInTheDocument()
    expect(screen.getByText(/← 后退5秒/)).toBeInTheDocument()
    expect(screen.getByText(/→ 前进5秒/)).toBeInTheDocument()
    expect(screen.getByText(/空格 播放\/暂停/)).toBeInTheDocument()
  })
})
