import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import VideoPlayer from '../src/components/VideoPlayer/VideoPlayer'

// Mock zustand store
vi.mock('../src/stores/subtitleStore', () => ({
  useSubtitleStore: () => ({
    setCurrentVideoTime: vi.fn(),
    startRecording: vi.fn(),
    stopRecording: vi.fn()
  })
}))

describe('VideoPlayer Component', () => {
  beforeEach(() => {
    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn(() => 'mock-video-url')
    global.URL.revokeObjectURL = vi.fn()
  })

  it('renders video upload interface initially', () => {
    render(<VideoPlayer />)
    
    expect(screen.getByText('请选择视频文件')).toBeInTheDocument()
    expect(screen.getByText('选择视频')).toBeInTheDocument()
  })

  it('shows video controls after file upload', () => {
    render(<VideoPlayer />)
    
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    
    fireEvent.change(fileInput, { target: { files: [mockFile] } })
    
    // Should show video element (though it won't actually play in test)
    expect(document.querySelector('video')).toBeInTheDocument()
  })

  it('handles play/pause button clicks', () => {
    render(<VideoPlayer />)

    // Upload a file first
    const fileInput = document.querySelector('input[type="file"]')
    const mockFile = new File(['video content'], 'test.mp4', { type: 'video/mp4' })
    fireEvent.change(fileInput, { target: { files: [mockFile] } })

    // Find play button by its SVG content (since it doesn't have accessible text)
    const playButton = document.querySelector('button svg[class*="lucide-play"]').parentElement
    expect(playButton).toBeInTheDocument()

    // Click the play button
    fireEvent.click(playButton)
  })
})
