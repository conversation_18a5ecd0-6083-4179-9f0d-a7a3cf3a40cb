/**
 * 快速FFmpeg诊断脚本
 * 在浏览器控制台中运行: 
 * 1. 打开开发者工具 (F12)
 * 2. 复制粘贴这个脚本到控制台
 * 3. 按回车执行
 */

async function quickFFmpegDiagnostic() {
    console.log('🔍 开始FFmpeg快速诊断...');
    
    const results = {
        webAssembly: false,
        ffmpegFiles: false,
        ffmpegModule: false,
        errors: []
    };
    
    try {
        // 1. 检查WebAssembly支持
        console.log('1️⃣ 检查WebAssembly支持...');
        results.webAssembly = typeof WebAssembly === 'object';
        console.log(`WebAssembly支持: ${results.webAssembly ? '✅' : '❌'}`);
        
        if (!results.webAssembly) {
            results.errors.push('浏览器不支持WebAssembly');
            console.error('❌ 浏览器不支持WebAssembly');
            return results;
        }
        
        // 2. 检查FFmpeg文件
        console.log('2️⃣ 检查FFmpeg核心文件...');
        try {
            const [coreResponse, wasmResponse] = await Promise.all([
                fetch('/ffmpeg/ffmpeg-core.js'),
                fetch('/ffmpeg/ffmpeg-core.wasm')
            ]);
            
            console.log(`FFmpeg JS文件: ${coreResponse.status} ${coreResponse.statusText}`);
            console.log(`FFmpeg WASM文件: ${wasmResponse.status} ${wasmResponse.statusText}`);
            
            if (coreResponse.ok && wasmResponse.ok) {
                results.ffmpegFiles = true;
                console.log('✅ FFmpeg核心文件可访问');
                
                // 显示文件大小
                const coreSize = coreResponse.headers.get('content-length');
                const wasmSize = wasmResponse.headers.get('content-length');
                if (coreSize) console.log(`JS文件大小: ${(coreSize/1024).toFixed(1)}KB`);
                if (wasmSize) console.log(`WASM文件大小: ${(wasmSize/1024/1024).toFixed(1)}MB`);
            } else {
                results.errors.push('FFmpeg核心文件不可访问');
                console.error('❌ FFmpeg核心文件不可访问');
            }
        } catch (fetchError) {
            results.errors.push(`文件检查失败: ${fetchError.message}`);
            console.error('❌ 文件检查失败:', fetchError);
        }
        
        // 3. 尝试加载FFmpeg模块
        console.log('3️⃣ 测试FFmpeg模块加载...');
        try {
            const { FFmpeg } = await import('https://unpkg.com/@ffmpeg/ffmpeg@0.12.10/dist/esm/index.js');
            console.log('✅ FFmpeg模块加载成功');
            
            // 尝试创建实例
            const ffmpeg = new FFmpeg();
            console.log('✅ FFmpeg实例创建成功');
            
            results.ffmpegModule = true;
            
        } catch (moduleError) {
            results.errors.push(`模块加载失败: ${moduleError.message}`);
            console.error('❌ FFmpeg模块加载失败:', moduleError);
        }
        
    } catch (error) {
        results.errors.push(`诊断异常: ${error.message}`);
        console.error('❌ 诊断异常:', error);
    }
    
    // 输出诊断结果
    console.log('\n📊 诊断结果汇总:');
    console.log(`WebAssembly支持: ${results.webAssembly ? '✅' : '❌'}`);
    console.log(`FFmpeg文件可访问: ${results.ffmpegFiles ? '✅' : '❌'}`);
    console.log(`FFmpeg模块加载: ${results.ffmpegModule ? '✅' : '❌'}`);
    
    if (results.errors.length > 0) {
        console.log('\n❌ 发现的问题:');
        results.errors.forEach((error, index) => {
            console.log(`${index + 1}. ${error}`);
        });
    }
    
    // 生成建议
    console.log('\n💡 建议:');
    if (!results.webAssembly) {
        console.log('- 请使用支持WebAssembly的现代浏览器 (Chrome, Firefox, Safari, Edge)');
    }
    if (!results.ffmpegFiles) {
        console.log('- 检查FFmpeg核心文件是否正确放置在 public/ffmpeg/ 目录');
        console.log('- 确保开发服务器正在运行');
        console.log('- 检查网络连接');
    }
    if (!results.ffmpegModule) {
        console.log('- 检查网络连接是否可以访问CDN');
        console.log('- 尝试刷新页面重新加载');
    }
    
    if (results.webAssembly && results.ffmpegFiles && results.ffmpegModule) {
        console.log('🎉 基础环境检查通过！可以尝试进行音频提取测试。');
    } else {
        console.log('⚠️ 发现问题，请根据上述建议进行修复。');
    }
    
    return results;
}

// 自动运行诊断
console.log('🚀 FFmpeg快速诊断工具');
console.log('正在运行诊断...');
quickFFmpegDiagnostic().then(results => {
    console.log('诊断完成！');
}).catch(error => {
    console.error('诊断失败:', error);
});

// 提供手动运行函数
window.quickFFmpegDiagnostic = quickFFmpegDiagnostic;
console.log('💡 你也可以手动运行: quickFFmpegDiagnostic()');
