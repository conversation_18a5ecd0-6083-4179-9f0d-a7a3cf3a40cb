<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f1f8e9; }
        .error { border-color: #f44336; background-color: #ffebee; }
        .warning { border-color: #ff9800; background-color: #fff3e0; }
        .info { border-color: #2196F3; background-color: #e3f2fd; }
        
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #1976D2; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        input[type="file"] {
            margin: 10px 0;
            padding: 5px;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FFmpeg 调试测试工具</h1>
        <p>这个工具可以帮助诊断FFmpeg音频提取过程中的问题。</p>
        
        <div class="test-section info">
            <h3>📁 选择测试文件</h3>
            <input type="file" id="videoFile" accept="video/*">
            <p><small>请选择一个视频文件进行测试（建议小于100MB）</small></p>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <button id="runBasicTest">运行基础测试</button>
            <button id="runFullTest" disabled>运行完整测试</button>
            <button id="clearLog">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>📊 测试进度</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            <div id="progressText">等待开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="log" id="testLog">等待测试开始...\n</div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="testResults">暂无结果</div>
        </div>
    </div>

    <script type="module">
        // 简单的日志系统
        const logElement = document.getElementById('testLog');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const resultsElement = document.getElementById('testResults');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateProgress(percent, message) {
            progressBar.style.width = `${percent}%`;
            progressText.textContent = message;
        }
        
        function clearLog() {
            logElement.textContent = '';
            resultsElement.innerHTML = '暂无结果';
            updateProgress(0, '等待开始测试...');
        }
        
        // 基础测试
        async function runBasicTest() {
            try {
                log('开始基础测试...', 'info');
                updateProgress(10, '检查浏览器支持...');

                // 检查WebAssembly支持
                if (typeof WebAssembly !== 'object') {
                    log('浏览器不支持WebAssembly', 'error');
                    updateProgress(0, '测试失败');
                    return false;
                }
                log('✅ WebAssembly支持正常', 'success');
                updateProgress(30, 'WebAssembly支持正常');

                // 检查FFmpeg文件
                updateProgress(50, '检查FFmpeg核心文件...');

                try {
                    const coreResponse = await fetch('/ffmpeg/ffmpeg-core.js');
                    const wasmResponse = await fetch('/ffmpeg/ffmpeg-core.wasm');

                    log(`FFmpeg JS文件状态: ${coreResponse.status}`, coreResponse.ok ? 'success' : 'error');
                    log(`FFmpeg WASM文件状态: ${wasmResponse.status}`, wasmResponse.ok ? 'success' : 'error');

                    if (!coreResponse.ok) {
                        log('❌ FFmpeg核心JS文件不可访问', 'error');
                        updateProgress(0, '测试失败');
                        return false;
                    }

                    if (!wasmResponse.ok) {
                        log('❌ FFmpeg核心WASM文件不可访问', 'error');
                        updateProgress(0, '测试失败');
                        return false;
                    }

                    // 检查文件大小
                    const coreSize = coreResponse.headers.get('content-length');
                    const wasmSize = wasmResponse.headers.get('content-length');

                    if (coreSize) {
                        log(`FFmpeg JS文件大小: ${(coreSize/1024).toFixed(1)}KB`, 'info');
                    }
                    if (wasmSize) {
                        log(`FFmpeg WASM文件大小: ${(wasmSize/1024/1024).toFixed(1)}MB`, 'info');
                    }

                    log('✅ FFmpeg核心文件可访问', 'success');
                    updateProgress(80, 'FFmpeg核心文件检查完成');

                } catch (fetchError) {
                    log(`❌ FFmpeg文件检查失败: ${fetchError.message}`, 'error');
                    updateProgress(0, '测试失败');
                    return false;
                }

                // 检查是否可以加载FFmpeg模块
                updateProgress(90, '测试FFmpeg模块加载...');
                try {
                    // 尝试动态导入FFmpeg
                    const ffmpegModule = await import('https://unpkg.com/@ffmpeg/ffmpeg@0.12.10/dist/esm/index.js');
                    log('✅ FFmpeg模块可以加载', 'success');
                } catch (moduleError) {
                    log(`⚠️ FFmpeg模块加载测试失败: ${moduleError.message}`, 'warning');
                    log('这可能不影响本地FFmpeg文件的使用', 'info');
                }

                updateProgress(100, '基础测试完成');
                log('🎉 基础测试完成', 'success');
                return true;

            } catch (error) {
                log(`❌ 基础测试异常: ${error.message}`, 'error');
                updateProgress(0, '测试失败');
                return false;
            }
        }
        
        // 完整测试
        async function runFullTest() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];

            if (!file) {
                log('❌ 请先选择一个视频文件', 'error');
                return;
            }

            log(`🎬 开始完整测试，文件: ${file.name}`, 'info');
            updateProgress(5, '准备测试环境...');

            const results = {
                fileValidation: false,
                ffmpegSupport: false,
                ffmpegInit: false,
                fileWrite: false,
                commandExec: false,
                audioExtraction: false,
                errors: []
            };

            try {
                // 1. 文件验证
                updateProgress(10, '验证文件...');
                log('📁 验证文件信息...', 'info');

                if (file.size === 0) {
                    results.errors.push('文件大小为0');
                    log('❌ 文件大小为0', 'error');
                } else if (file.size > 500 * 1024 * 1024) {
                    results.errors.push('文件过大 (>500MB)');
                    log('❌ 文件过大', 'error');
                } else {
                    results.fileValidation = true;
                    log(`✅ 文件验证通过: ${(file.size/1024/1024).toFixed(2)}MB`, 'success');
                }

                // 2. FFmpeg支持检查
                updateProgress(20, '检查FFmpeg支持...');
                results.ffmpegSupport = typeof WebAssembly === 'object';
                log(`WebAssembly支持: ${results.ffmpegSupport ? '✅' : '❌'}`, results.ffmpegSupport ? 'success' : 'error');

                // 3. 尝试初始化FFmpeg
                updateProgress(30, '尝试初始化FFmpeg...');
                try {
                    log('🚀 尝试加载FFmpeg模块...', 'info');

                    // 尝试从CDN加载FFmpeg
                    const { FFmpeg } = await import('https://unpkg.com/@ffmpeg/ffmpeg@0.12.10/dist/esm/index.js');
                    const { toBlobURL } = await import('https://unpkg.com/@ffmpeg/util@0.12.1/dist/esm/index.js');

                    log('✅ FFmpeg模块加载成功', 'success');

                    const ffmpeg = new FFmpeg();

                    // 设置日志
                    ffmpeg.on('log', ({ message }) => {
                        log(`[FFmpeg] ${message}`, 'info');
                    });

                    updateProgress(50, '加载FFmpeg核心文件...');

                    // 尝试加载本地文件
                    await ffmpeg.load({
                        coreURL: await toBlobURL('/ffmpeg/ffmpeg-core.js', 'text/javascript'),
                        wasmURL: await toBlobURL('/ffmpeg/ffmpeg-core.wasm', 'application/wasm'),
                    });

                    results.ffmpegInit = true;
                    log('✅ FFmpeg初始化成功', 'success');

                    // 4. 测试文件写入
                    updateProgress(70, '测试文件写入...');
                    try {
                        const { fetchFile } = await import('https://unpkg.com/@ffmpeg/util@0.12.1/dist/esm/index.js');
                        const testFileName = 'test_input.mp4';

                        const fileData = await fetchFile(file);
                        await ffmpeg.writeFile(testFileName, fileData);

                        // 验证文件
                        const fileList = await ffmpeg.listDir('/');
                        const writtenFile = fileList.find(f => f.name === testFileName);

                        if (writtenFile) {
                            results.fileWrite = true;
                            log(`✅ 文件写入成功: ${writtenFile.size} bytes`, 'success');

                            // 5. 测试命令执行
                            updateProgress(85, '测试FFmpeg命令...');
                            try {
                                await ffmpeg.exec(['-version']);
                                results.commandExec = true;
                                log('✅ FFmpeg命令执行成功', 'success');

                                // 6. 尝试简单的音频提取
                                updateProgress(95, '测试音频提取...');
                                try {
                                    await ffmpeg.exec([
                                        '-i', testFileName,
                                        '-t', '1',  // 只提取1秒
                                        '-vn',
                                        '-acodec', 'pcm_s16le',
                                        '-ar', '16000',
                                        '-ac', '1',
                                        '-y',
                                        'test_output.wav'
                                    ]);

                                    const outputData = await ffmpeg.readFile('test_output.wav');
                                    if (outputData.length > 0) {
                                        results.audioExtraction = true;
                                        log(`✅ 音频提取成功: ${outputData.length} bytes`, 'success');
                                    } else {
                                        log('❌ 音频提取结果为空', 'error');
                                        results.errors.push('音频提取结果为空');
                                    }
                                } catch (extractError) {
                                    log(`❌ 音频提取失败: ${extractError.message}`, 'error');
                                    results.errors.push(`音频提取失败: ${extractError.message}`);
                                }
                            } catch (cmdError) {
                                log(`❌ FFmpeg命令执行失败: ${cmdError.message}`, 'error');
                                results.errors.push(`命令执行失败: ${cmdError.message}`);
                            }

                            // 清理测试文件
                            try {
                                await ffmpeg.deleteFile(testFileName);
                                await ffmpeg.deleteFile('test_output.wav');
                            } catch (e) {
                                // 忽略清理错误
                            }

                        } else {
                            log('❌ 文件写入验证失败', 'error');
                            results.errors.push('文件写入验证失败');
                        }
                    } catch (writeError) {
                        log(`❌ 文件写入失败: ${writeError.message}`, 'error');
                        results.errors.push(`文件写入失败: ${writeError.message}`);
                    }

                } catch (initError) {
                    log(`❌ FFmpeg初始化失败: ${initError.message}`, 'error');
                    results.errors.push(`FFmpeg初始化失败: ${initError.message}`);
                }

                updateProgress(100, '测试完成');

                // 显示结果
                let resultHtml = '<h4>🧪 测试结果:</h4><ul>';
                resultHtml += `<li>文件验证: ${results.fileValidation ? '✅' : '❌'}</li>`;
                resultHtml += `<li>FFmpeg支持: ${results.ffmpegSupport ? '✅' : '❌'}</li>`;
                resultHtml += `<li>FFmpeg初始化: ${results.ffmpegInit ? '✅' : '❌'}</li>`;
                resultHtml += `<li>文件写入: ${results.fileWrite ? '✅' : '❌'}</li>`;
                resultHtml += `<li>命令执行: ${results.commandExec ? '✅' : '❌'}</li>`;
                resultHtml += `<li>音频提取: ${results.audioExtraction ? '✅' : '❌'}</li>`;
                resultHtml += '</ul>';

                if (results.errors.length > 0) {
                    resultHtml += '<h4>❌ 错误信息:</h4><ul>';
                    results.errors.forEach(error => {
                        resultHtml += `<li style="color: red;">${error}</li>`;
                    });
                    resultHtml += '</ul>';
                }

                // 生成建议
                const recommendations = [];
                if (!results.ffmpegSupport) {
                    recommendations.push('请使用支持WebAssembly的现代浏览器');
                }
                if (!results.fileValidation) {
                    recommendations.push('请检查视频文件是否有效且格式受支持');
                }
                if (!results.ffmpegInit) {
                    recommendations.push('请检查网络连接和FFmpeg核心文件是否可访问');
                }
                if (!results.fileWrite) {
                    recommendations.push('可能是内存不足或文件过大');
                }
                if (!results.commandExec) {
                    recommendations.push('FFmpeg核心可能损坏，请重新下载');
                }
                if (!results.audioExtraction) {
                    recommendations.push('视频格式可能不受支持，请尝试转换为MP4格式');
                }

                if (recommendations.length > 0) {
                    resultHtml += '<h4>💡 建议:</h4><ul>';
                    recommendations.forEach(rec => {
                        resultHtml += `<li style="color: orange;">${rec}</li>`;
                    });
                    resultHtml += '</ul>';
                }

                resultsElement.innerHTML = resultHtml;

                log('🎉 完整测试完成', 'success');

            } catch (error) {
                log(`❌ 完整测试失败: ${error.message}`, 'error');
                updateProgress(0, '测试失败');

                resultsElement.innerHTML = `<h4>❌ 测试失败</h4><p style="color: red;">${error.message}</p>`;
            }
        }
        
        // 事件监听器
        document.getElementById('runBasicTest').addEventListener('click', async () => {
            const button = document.getElementById('runBasicTest');
            button.disabled = true;
            
            const success = await runBasicTest();
            if (success) {
                document.getElementById('runFullTest').disabled = false;
            }
            
            button.disabled = false;
        });
        
        document.getElementById('runFullTest').addEventListener('click', async () => {
            const button = document.getElementById('runFullTest');
            button.disabled = true;
            await runFullTest();
            button.disabled = false;
        });
        
        document.getElementById('clearLog').addEventListener('click', clearLog);
        
        // 文件选择事件
        document.getElementById('videoFile').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                log(`选择文件: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            }
        });
        
        log('FFmpeg调试工具已加载', 'success');
    </script>
</body>
</html>
