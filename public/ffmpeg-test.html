<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f1f8e9; }
        .error { border-color: #f44336; background-color: #ffebee; }
        .warning { border-color: #ff9800; background-color: #fff3e0; }
        .info { border-color: #2196F3; background-color: #e3f2fd; }
        
        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #1976D2; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        input[type="file"] {
            margin: 10px 0;
            padding: 5px;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FFmpeg 调试测试工具</h1>
        <p>这个工具可以帮助诊断FFmpeg音频提取过程中的问题。</p>
        
        <div class="test-section info">
            <h3>📁 选择测试文件</h3>
            <input type="file" id="videoFile" accept="video/*">
            <p><small>请选择一个视频文件进行测试（建议小于100MB）</small></p>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <button id="runBasicTest">运行基础测试</button>
            <button id="runFullTest" disabled>运行完整测试</button>
            <button id="clearLog">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>📊 测试进度</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
            <div id="progressText">等待开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="log" id="testLog">等待测试开始...\n</div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="testResults">暂无结果</div>
        </div>
    </div>

    <script type="module">
        // 简单的日志系统
        const logElement = document.getElementById('testLog');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const resultsElement = document.getElementById('testResults');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateProgress(percent, message) {
            progressBar.style.width = `${percent}%`;
            progressText.textContent = message;
        }
        
        function clearLog() {
            logElement.textContent = '';
            resultsElement.innerHTML = '暂无结果';
            updateProgress(0, '等待开始测试...');
        }
        
        // 基础测试
        async function runBasicTest() {
            log('开始基础测试...', 'info');
            updateProgress(10, '检查浏览器支持...');
            
            // 检查WebAssembly支持
            if (typeof WebAssembly !== 'object') {
                log('浏览器不支持WebAssembly', 'error');
                return false;
            }
            log('WebAssembly支持正常', 'success');
            updateProgress(30, 'WebAssembly支持正常');
            
            // 检查FFmpeg文件
            try {
                updateProgress(50, '检查FFmpeg核心文件...');
                const coreResponse = await fetch('/ffmpeg/ffmpeg-core.js');
                const wasmResponse = await fetch('/ffmpeg/ffmpeg-core.wasm');
                
                if (!coreResponse.ok || !wasmResponse.ok) {
                    log('FFmpeg核心文件不可访问', 'error');
                    return false;
                }
                
                log(`FFmpeg核心文件可访问 (JS: ${coreResponse.status}, WASM: ${wasmResponse.status})`, 'success');
                updateProgress(80, 'FFmpeg核心文件检查完成');
                
            } catch (error) {
                log(`FFmpeg文件检查失败: ${error.message}`, 'error');
                return false;
            }
            
            updateProgress(100, '基础测试完成');
            log('基础测试完成', 'success');
            return true;
        }
        
        // 完整测试
        async function runFullTest() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];
            
            if (!file) {
                log('请先选择一个视频文件', 'error');
                return;
            }
            
            log(`开始完整测试，文件: ${file.name}`, 'info');
            updateProgress(5, '准备测试环境...');
            
            try {
                // 动态导入调试器
                const { default: FFmpegDebugger } = await import('/src/utils/ffmpegDebugger.js');
                const debugger = new FFmpegDebugger();
                
                updateProgress(10, '运行诊断测试...');
                
                const results = await debugger.runDiagnostics(file);
                const report = debugger.generateReport(results);
                
                updateProgress(100, '测试完成');
                
                // 显示结果
                let resultHtml = '<h4>测试结果:</h4><ul>';
                resultHtml += `<li>文件验证: ${results.fileValidation ? '✅' : '❌'}</li>`;
                resultHtml += `<li>FFmpeg支持: ${results.ffmpegSupport ? '✅' : '❌'}</li>`;
                resultHtml += `<li>FFmpeg初始化: ${results.ffmpegInit ? '✅' : '❌'}</li>`;
                resultHtml += `<li>文件写入: ${results.fileWrite ? '✅' : '❌'}</li>`;
                resultHtml += `<li>命令执行: ${results.commandExec ? '✅' : '❌'}</li>`;
                resultHtml += `<li>音频提取: ${results.audioExtraction ? '✅' : '❌'}</li>`;
                resultHtml += '</ul>';
                
                if (results.errors.length > 0) {
                    resultHtml += '<h4>错误信息:</h4><ul>';
                    results.errors.forEach(error => {
                        resultHtml += `<li style="color: red;">${error}</li>`;
                    });
                    resultHtml += '</ul>';
                }
                
                if (report.recommendations.length > 0) {
                    resultHtml += '<h4>建议:</h4><ul>';
                    report.recommendations.forEach(rec => {
                        resultHtml += `<li style="color: orange;">${rec}</li>`;
                    });
                    resultHtml += '</ul>';
                }
                
                resultsElement.innerHTML = resultHtml;
                
                log('完整测试完成', 'success');
                
            } catch (error) {
                log(`完整测试失败: ${error.message}`, 'error');
                updateProgress(0, '测试失败');
            }
        }
        
        // 事件监听器
        document.getElementById('runBasicTest').addEventListener('click', async () => {
            const button = document.getElementById('runBasicTest');
            button.disabled = true;
            
            const success = await runBasicTest();
            if (success) {
                document.getElementById('runFullTest').disabled = false;
            }
            
            button.disabled = false;
        });
        
        document.getElementById('runFullTest').addEventListener('click', async () => {
            const button = document.getElementById('runFullTest');
            button.disabled = true;
            await runFullTest();
            button.disabled = false;
        });
        
        document.getElementById('clearLog').addEventListener('click', clearLog);
        
        // 文件选择事件
        document.getElementById('videoFile').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                log(`选择文件: ${file.name} (${(file.size/1024/1024).toFixed(2)}MB)`, 'info');
            }
        });
        
        log('FFmpeg调试工具已加载', 'success');
    </script>
</body>
</html>
