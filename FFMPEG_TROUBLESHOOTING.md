# FFmpeg 音频提取问题排查指南

## 🚨 常见错误："File could not be read! Code=-1"

这个错误通常表示FFmpeg无法读取输入文件。以下是详细的排查步骤和解决方案。

## 🔍 问题诊断步骤

### 1. 使用调试工具
访问 `http://localhost:5173/ffmpeg-test.html` 运行诊断测试：

```bash
# 启动开发服务器
npm run dev

# 在浏览器中访问
http://localhost:5173/ffmpeg-test.html
```

### 2. 检查浏览器控制台
打开浏览器开发者工具，查看控制台输出的详细错误信息。

## 🛠️ 常见原因和解决方案

### 原因1: 文件格式不支持
**症状**: 某些视频格式无法被FFmpeg处理
**解决方案**:
- 尝试使用MP4格式的视频文件
- 确保视频文件没有损坏
- 检查视频编码格式是否受支持

### 原因2: 文件过大
**症状**: 大文件导致内存不足
**解决方案**:
- 限制文件大小在500MB以下
- 使用较短的视频片段进行测试
- 考虑在服务器端处理大文件

### 原因3: FFmpeg核心文件问题
**症状**: FFmpeg初始化失败或文件无法访问
**解决方案**:
```bash
# 检查FFmpeg文件是否存在
ls -la public/ffmpeg/

# 重新下载FFmpeg核心文件
curl -L https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd/ffmpeg-core.js -o public/ffmpeg/ffmpeg-core.js
curl -L https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd/ffmpeg-core.wasm -o public/ffmpeg/ffmpeg-core.wasm
```

### 原因4: 浏览器兼容性
**症状**: 在某些浏览器中无法工作
**解决方案**:
- 使用Chrome或Edge浏览器（推荐）
- 确保浏览器支持WebAssembly
- 检查浏览器版本是否过旧

### 原因5: 内存限制
**症状**: 处理过程中内存不足
**解决方案**:
- 关闭其他占用内存的标签页
- 重启浏览器释放内存
- 使用较小的视频文件进行测试

## 🔧 代码级别的改进

### 1. 增强错误处理
已在 `ffmpegAudioExtractor.js` 中添加了以下改进：
- 文件验证和调试信息
- 更详细的错误消息
- FFmpeg命令执行的错误捕获
- 文件写入验证

### 2. 添加兼容性选项
FFmpeg命令中添加了更多兼容性参数：
```javascript
args.push(
  '-avoid_negative_ts', 'make_zero',  // 避免负时间戳
  '-fflags', '+genpts',               // 生成时间戳
  '-f', this.getInputFormat(fileExt)  // 明确指定输入格式
)
```

### 3. 文件格式检测
添加了输入格式自动检测：
```javascript
getInputFormat(extension) {
  const formats = {
    'mp4': 'mp4',
    'avi': 'avi',
    'mov': 'mov',
    'mkv': 'matroska',
    'webm': 'webm'
    // ... 更多格式
  }
  return formats[extension] || 'auto'
}
```

## 🧪 测试建议

### 1. 使用测试文件
创建一个小的测试视频文件：
```bash
# 使用FFmpeg创建测试文件（如果有FFmpeg命令行工具）
ffmpeg -f lavfi -i testsrc=duration=10:size=320x240:rate=1 -c:v libx264 test.mp4
```

### 2. 逐步测试
1. 先测试小文件（<10MB）
2. 测试不同格式（MP4, AVI, MOV）
3. 测试不同时长（5秒, 30秒, 5分钟）

### 3. 监控资源使用
- 观察浏览器内存使用情况
- 检查网络请求是否成功
- 监控FFmpeg初始化过程

## 📋 故障排除清单

- [ ] 浏览器支持WebAssembly
- [ ] FFmpeg核心文件可访问
- [ ] 视频文件格式受支持
- [ ] 文件大小在限制范围内
- [ ] 文件没有损坏
- [ ] 有足够的可用内存
- [ ] 网络连接正常
- [ ] 没有浏览器扩展干扰

## 🆘 获取帮助

如果问题仍然存在，请提供以下信息：

1. **浏览器信息**: 版本和类型
2. **文件信息**: 格式、大小、来源
3. **错误日志**: 完整的控制台输出
4. **测试结果**: 调试工具的输出
5. **系统信息**: 操作系统和可用内存

## 🔄 替代方案

如果FFmpeg方案无法解决，可以考虑：

1. **服务器端处理**: 在后端使用FFmpeg处理
2. **云端服务**: 使用云端音频处理API
3. **简化功能**: 仅支持特定格式的文件
4. **外部工具**: 指导用户使用外部工具提取音频

## 📚 相关资源

- [FFmpeg.wasm 官方文档](https://ffmpegwasm.netlify.app/)
- [WebAssembly 兼容性](https://caniuse.com/wasm)
- [浏览器内存限制](https://developer.mozilla.org/en-US/docs/Web/API/Performance/memory)

---

通过以上步骤，应该能够诊断和解决大部分FFmpeg音频提取问题。如果问题持续存在，建议使用调试工具进行详细分析。
