#!/bin/bash

echo "🚀 启动Chrome开发模式（禁用CORS）"

# 关闭所有Chrome实例
echo "📝 关闭现有Chrome实例..."
pkill -f "Google Chrome" 2>/dev/null || true

# 等待进程完全关闭
sleep 3

# 创建临时用户数据目录
USER_DATA_DIR="$(pwd)/.chrome-dev-data"
mkdir -p "$USER_DATA_DIR"

echo "🌐 启动Chrome（开发模式）..."

# 启动Chrome（开发模式，禁用CORS）
"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" \
  --disable-web-security \
  --disable-features=VizDisplayCompositor \
  --allow-running-insecure-content \
  --disable-site-isolation-trials \
  --disable-same-origin-policy \
  --user-data-dir="$USER_DATA_DIR" \
  --no-first-run \
  --no-default-browser-check \
  --disable-background-timer-throttling \
  --disable-backgrounding-occluded-windows \
  --disable-renderer-backgrounding \
  http://localhost:5173 &

echo "✅ Chrome已启动（开发模式）"
echo "📍 访问地址: http://localhost:5173"
echo "🔧 现在可以测试Whisper连接了"
echo "⚠️  注意：此Chrome实例已禁用安全策略，仅用于开发测试"
