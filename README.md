# 视频实时字幕生成器

一个基于 React 的视频实时字幕生成应用，支持语音识别、字幕翻译和导出功能。

## ✨ 功能特性

- 🎥 **视频播放器** - 支持多种视频格式，完整的播放控制
- 🎤 **实时语音识别** - 基于 Web Speech API 的实时语音转文字
- 🌐 **字幕翻译** - 支持多语言实时翻译
- 💾 **字幕导出** - 支持 SRT、VTT 格式导出
- ✏️ **字幕编辑** - 可编辑和删除生成的字幕
- 📱 **响应式设计** - 适配桌面和移动设备

## 🚀 快速开始

### 环境要求

- Node.js 16+
- 现代浏览器（推荐 Chrome 或 Edge，支持 Web Speech API）

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
npm test
```

## 🛠️ 技术栈

- **前端框架**: React 18 + Vite
- **状态管理**: Zustand
- **样式框架**: Tailwind CSS
- **图标库**: Lucide React
- **语音识别**: Web Speech API
- **文件处理**: FileSaver.js
- **测试框架**: Vitest + Testing Library

## 📁 项目结构

```
video-subtitles/
├── src/
│   ├── components/              # React 组件
│   │   ├── VideoPlayer/         # 视频播放器组件
│   │   │   └── VideoPlayer.jsx  # 视频播放和控制
│   │   ├── SubtitleDisplay/     # 字幕显示组件
│   │   │   └── SubtitleDisplay.jsx # 字幕显示和编辑
│   │   ├── ControlPanel/        # 控制面板组件
│   │   │   └── ControlPanel.jsx # 设置和操作面板
│   │   └── Settings/            # 设置组件
│   ├── services/                # 服务层
│   │   └── speechRecognitionService.js # 语音识别服务
│   ├── stores/                  # Zustand 状态管理
│   │   └── subtitleStore.js     # 字幕状态管理
│   ├── hooks/                   # 自定义 Hooks
│   ├── utils/                   # 工具函数
│   ├── App.jsx                  # 主应用组件
│   └── main.jsx                 # 应用入口
├── tests/                       # 测试文件
│   ├── VideoPlayer.test.jsx     # 视频播放器测试
│   ├── SubtitleStore.test.js    # 字幕状态测试
│   └── SpeechRecognitionService.test.js # 语音识别服务测试
├── public/                      # 静态资源
└── README.md                   # 项目文档
```

## 🎯 使用指南

### 语音识别模式

应用提供多种语音识别模式：

#### 🖥️ 浏览器模式
- **用途**: 实时语音识别生成字幕
- **特点**: 使用浏览器内置语音识别API，需要麦克风权限
- **使用场景**: 为无声视频添加解说字幕、会议记录等

#### 🏠 局域网模式 (推荐)
- **用途**: 高精度视频音频识别
- **特点**: 使用局域网Whisper服务，可直接处理视频音频
- **使用场景**: 视频字幕生成、隐私敏感场景

#### ☁️ 云端模式
- **用途**: 高精度语音识别
- **特点**: 使用云端API，识别准确度最高
- **使用场景**: 生产环境、批量处理

### 详细操作步骤

#### 1. 上传视频
- 点击"选择视频"按钮上传视频文件
- 支持常见视频格式（MP4、WebM、AVI 等）
- 视频会在播放器中显示

#### 2. 选择识别模式
- 在控制面板中选择合适的识别模式
- **浏览器模式**: 需要麦克风权限，适合实时解说
- **局域网模式**: 需要配置Whisper服务，可直接处理视频音频
- **云端模式**: 需要API密钥，识别准确度最高

#### 3. 开始生成字幕
- 点击播放按钮开始视频播放
- 系统会根据选择的识别模式进行字幕生成
- 字幕会实时显示在视频下方

#### 4. 语言设置
- 在控制面板中选择识别语言
- 支持中文、英文、日文、韩文等14种语言
- 语音识别模式下会影响识别准确度

#### 5. 字幕翻译
- 启用翻译功能开关
- 选择目标翻译语言
- 点击"批量翻译"翻译所有字幕
- 支持单条字幕翻译

#### 6. 编辑字幕
- 在字幕历史列表中点击编辑按钮（✏️）
- 修改字幕内容并保存
- 可删除不需要的字幕（🗑️）
- 支持调整字幕时间戳

#### 7. 导出字幕
- 点击"导出SRT"或"导出VTT"按钮
- 字幕文件会自动下载到本地
- 包含时间戳和字幕内容

## 🔧 配置说明

### 语音识别配置
应用使用浏览器原生的 Web Speech API：
- **支持的浏览器**: Chrome、Edge（推荐）、Safari
- **识别语言**: 支持14种语言，可在设置中选择
- **实时识别**: 连续语音识别，自动分段
- **权限要求**: 需要麦克风访问权限

### 技术解决方案

应用提供三种语音识别模式来解决技术限制：

#### 🖥️ 浏览器模式
- **特点**: 使用浏览器内置语音识别API
- **优势**: 无需配置，实时识别，隐私保护
- **适用**: 麦克风实时输入，演示和测试
- **限制**: 主要支持麦克风输入，准确度有限

#### 💻 离线模式 (实验性)
- **特点**: 使用WebAssembly技术实现离线识别
- **优势**: 完全离线，可处理视频音频，高准确度
- **适用**: 隐私敏感场景，无网络环境
- **限制**: 模型文件较大，首次加载慢

#### ☁️ 云端模式
- **特点**: 集成多个云端语音识别API
- **优势**: 识别准确度最高，支持多种语言
- **适用**: 生产环境，批量处理
- **限制**: 需要API密钥和网络连接

#### 🏠 局域网模式 (推荐)
- **特点**: 在局域网内部署Whisper模型服务
- **优势**: 高准确度，数据不出局域网，可处理视频音频
- **适用**: 隐私敏感场景，企业内部使用
- **限制**: 需要额外设备部署服务

#### 推荐工作流程
1. **实时解说**: 使用浏览器模式 + 麦克风
2. **视频处理**:
   - **推荐**: 部署局域网Whisper服务，直接处理视频音频
   - **备选**: 使用云端模式或外部工具链（FFmpeg提取音频 → 云端识别 → 导入编辑）
3. **离线处理**: 配置WebAssembly模式（需要技术配置）

### 翻译功能
当前使用模拟翻译，实际项目中可集成：
- Google Translate API
- OpenAI GPT API
- 百度翻译 API
- 腾讯翻译 API
- Azure Translator

## 🧪 测试

项目包含完整的单元测试：

```bash
# 运行所有测试
npm test

# 运行测试并查看覆盖率
npm run test:coverage

# 交互式测试界面
npm run test:ui
```

测试覆盖：
- 视频播放器组件测试
- 字幕显示组件测试
- 状态管理测试

## ❓ 常见问题

### Q: 为什么无法从视频中直接提取语音进行识别？
A: 由于浏览器安全策略限制，Web应用无法直接访问视频文件的音频流进行语音识别。当前的语音识别功能主要用于麦克风实时输入。

### Q: 如何处理视频文件中的语音内容？
A: 推荐使用以下工作流程：
1. 使用FFmpeg等工具提取视频音频
2. 使用云端语音识别服务处理音频
3. 将结果导入本应用进行编辑

### Q: 支持哪些浏览器？
A: 推荐使用Chrome或Edge浏览器，这些浏览器对Web Speech API支持最好。Safari也支持但功能可能有限。

### Q: 语音识别准确度如何提高？
A: 建议：
- 使用清晰的麦克风
- 在安静的环境中录制
- 说话清晰、语速适中
- 选择正确的识别语言

### Q: 翻译功能是否支持离线使用？
A: 当前版本使用模拟翻译。实际部署时需要集成在线翻译API，因此需要网络连接。

### Q: 如何启用云端识别功能？
A: 需要配置API密钥：
1. 复制 `.env.example` 为 `.env`
2. 填入相应的API密钥
3. 重启应用

### Q: 离线模式如何配置？
A: 离线模式需要WebAssembly模型文件：
1. 下载Whisper WASM模型
2. 放置到 `public/models/` 目录
3. 设置环境变量 `VITE_ENABLE_OFFLINE_RECOGNITION=true`

### Q: 如何部署局域网Whisper服务？
A: 详细步骤请参考 `LOCAL_WHISPER_DEPLOYMENT.md`：
1. 在局域网设备上部署Whisper服务
2. 在前端应用中配置服务器地址
3. 选择"局域网识别"模式即可使用

## 🔧 高级配置

### 云端API配置

1. **创建环境配置文件**:
```bash
cp .env.example .env
```

2. **配置API密钥**:
```bash
# Google Speech-to-Text
VITE_GOOGLE_SPEECH_API_KEY=your_google_api_key

# OpenAI Whisper
VITE_OPENAI_API_KEY=your_openai_api_key

# Azure Speech Services
VITE_AZURE_SPEECH_API_KEY=your_azure_key
VITE_AZURE_SPEECH_REGION=eastus
```

3. **重启应用**:
```bash
npm run dev
```

### 离线识别配置

1. **下载模型文件** (可选):
```bash
# 创建模型目录
mkdir -p public/models

# 下载Whisper模型 (示例)
wget https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.bin \
     -O public/models/ggml-base.bin
```

2. **启用离线模式**:
```bash
echo "VITE_ENABLE_OFFLINE_RECOGNITION=true" >> .env
```

### 局域网Whisper服务配置

1. **部署服务端**:
```bash
# 进入服务端目录
cd whisper-server

# 安装依赖
./start.sh --install-deps

# 启动服务（默认端口8000）
./start.sh

# 或指定配置
WHISPER_MODEL_SIZE=small WHISPER_PORT=8080 ./start.sh
```

2. **前端配置**:
- 在控制面板中选择"局域网识别"模式
- 点击"配置"按钮
- 使用自动发现或手动输入服务器地址
- 连接成功后即可使用

3. **详细部署指南**: 参考 `LOCAL_WHISPER_DEPLOYMENT.md`

### 性能优化配置

```bash
# 启用音频压缩
VITE_ENABLE_AUDIO_COMPRESSION=true

# 设置最大音频长度（秒）
VITE_MAX_AUDIO_DURATION=300

# 启用缓存
VITE_ENABLE_CACHE=true
```

## 🚀 部署

### Vercel 部署
```bash
npm install -g vercel
vercel
```

### Netlify 部署
```bash
npm run build
# 上传 dist 文件夹到 Netlify
```

### GitHub Pages 部署
```bash
npm run build
# 配置 GitHub Actions 自动部署
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📝 更新日志

### v1.2.1 (2024-06-22)
- ✨ 新增音量控制拖动时显示百分比功能
- 🎨 优化音量控制UI设计和用户体验
- 🧪 添加音量控制相关测试用例
- 📱 确保移动设备和桌面设备兼容性

### v1.2.0 (2024-06-22)
- 🗑️ 移除演示模式，简化应用架构
- 🔧 优化语音识别服务架构
- 📚 更新使用文档和技术说明
- 🧪 更新单元测试
- 🎯 专注于实际语音识别功能

### v1.1.0 (2024-06-19)
- 🔧 重构语音识别服务架构
- 🎭 添加演示模式功能
- 📚 完善使用文档和技术说明
- 🧪 添加全面的单元测试
- 🐛 修复字幕时间同步问题
- 💡 添加技术限制说明和推荐工作流程

### v1.0.0 (2024-06-18)
- ✨ 初始版本发布
- 🎥 视频播放器功能
- 🎤 实时语音识别
- 🌐 字幕翻译功能
- 💾 字幕导出功能
- ✏️ 字幕编辑功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [React](https://reactjs.org/) - 用户界面库
- [Vite](https://vitejs.dev/) - 构建工具
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Zustand](https://github.com/pmndrs/zustand) - 状态管理
- [Lucide](https://lucide.dev/) - 图标库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-username/video-subtitles/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
