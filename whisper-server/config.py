"""
Whisper服务配置文件
"""

import os
from typing import List

class WhisperConfig:
    """Whisper服务配置类"""
    
    # 模型配置
    MODEL_SIZE: str = os.getenv('WHISPER_MODEL_SIZE', 'base')
    MODEL_CACHE_DIR: str = os.getenv('WHISPER_CACHE_DIR', '~/.cache/whisper')
    
    # 服务配置
    HOST: str = os.getenv('WHISPER_HOST', '0.0.0.0')
    PORT: int = int(os.getenv('WHISPER_PORT', 8000))
    DEBUG: bool = os.getenv('WHISPER_DEBUG', 'false').lower() == 'true'
    
    # 文件处理配置
    MAX_FILE_SIZE: int = int(os.getenv('WHISPER_MAX_FILE_SIZE', 100 * 1024 * 1024))  # 100MB
    UPLOAD_FOLDER: str = os.getenv('WHISPER_UPLOAD_FOLDER', '/tmp/whisper')
    ALLOWED_EXTENSIONS: List[str] = [
        'wav', 'mp3', 'mp4', 'avi', 'mov', 'webm', 'm4a', 'flac', 'ogg'
    ]
    
    # 音频处理配置
    SAMPLE_RATE: int = int(os.getenv('WHISPER_SAMPLE_RATE', 16000))
    MAX_DURATION: int = int(os.getenv('WHISPER_MAX_DURATION', 600))  # 10分钟
    
    # 性能配置
    USE_GPU: bool = os.getenv('WHISPER_USE_GPU', 'auto').lower() != 'false'
    MAX_WORKERS: int = int(os.getenv('WHISPER_MAX_WORKERS', 4))
    
    # 安全配置
    ALLOWED_ORIGINS: List[str] = os.getenv('WHISPER_ALLOWED_ORIGINS', '*').split(',')
    API_KEY: str = os.getenv('WHISPER_API_KEY', '')  # 可选的API密钥
    
    # 日志配置
    LOG_LEVEL: str = os.getenv('WHISPER_LOG_LEVEL', 'INFO')
    LOG_FILE: str = os.getenv('WHISPER_LOG_FILE', '')
    
    @classmethod
    def validate(cls):
        """验证配置"""
        errors = []
        
        if cls.MODEL_SIZE not in ['tiny', 'base', 'small', 'medium', 'large']:
            errors.append(f"无效的模型大小: {cls.MODEL_SIZE}")
        
        if cls.PORT < 1 or cls.PORT > 65535:
            errors.append(f"无效的端口号: {cls.PORT}")
        
        if cls.MAX_FILE_SIZE <= 0:
            errors.append(f"无效的最大文件大小: {cls.MAX_FILE_SIZE}")
        
        if cls.MAX_DURATION <= 0:
            errors.append(f"无效的最大音频长度: {cls.MAX_DURATION}")
        
        if errors:
            raise ValueError("配置验证失败:\n" + "\n".join(errors))
        
        return True

# 全局配置实例
config = WhisperConfig()

# 模型大小对应的内存需求（大概值）
MODEL_MEMORY_REQUIREMENTS = {
    'tiny': '1GB',
    'base': '2GB', 
    'small': '3GB',
    'medium': '6GB',
    'large': '12GB'
}

# 支持的语言列表
SUPPORTED_LANGUAGES = {
    'auto': '自动检测',
    'zh': '中文',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский',
    'it': 'Italiano',
    'pt': 'Português',
    'ar': 'العربية',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'vi': 'Tiếng Việt'
}
