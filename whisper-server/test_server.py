#!/usr/bin/env python3
"""
Whisper服务测试脚本
用于测试服务器功能和性能
"""

import os
import sys
import time
import requests
import json
from pathlib import Path

class WhisperServerTester:
    def __init__(self, server_url="http://localhost:8000"):
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()
        
    def test_health(self):
        """测试健康检查接口"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.server_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务健康: {data}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def test_info(self):
        """测试服务信息接口"""
        print("\n📋 测试服务信息...")
        try:
            response = self.session.get(f"{self.server_url}/info", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务信息:")
                for key, value in data.items():
                    print(f"   {key}: {value}")
                return True
            else:
                print(f"❌ 获取信息失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def create_test_audio(self):
        """创建测试音频文件"""
        print("\n🎵 创建测试音频...")
        try:
            import numpy as np
            import wave
            
            # 生成1秒的正弦波音频
            sample_rate = 16000
            duration = 1.0
            frequency = 440  # A4音符
            
            t = np.linspace(0, duration, int(sample_rate * duration))
            audio_data = np.sin(2 * np.pi * frequency * t)
            
            # 转换为16位整数
            audio_data = (audio_data * 32767).astype(np.int16)
            
            # 保存为WAV文件
            test_file = "test_audio.wav"
            with wave.open(test_file, 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            print(f"✅ 测试音频已创建: {test_file}")
            return test_file
            
        except ImportError:
            print("⚠️  numpy/wave模块未安装，跳过音频生成")
            return None
        except Exception as e:
            print(f"❌ 创建音频失败: {e}")
            return None
    
    def test_transcribe(self, audio_file=None):
        """测试语音识别接口"""
        print("\n🎤 测试语音识别...")
        
        if audio_file is None:
            audio_file = self.create_test_audio()
        
        if audio_file is None or not os.path.exists(audio_file):
            print("❌ 没有可用的测试音频文件")
            return False
        
        try:
            with open(audio_file, 'rb') as f:
                files = {'audio': f}
                data = {
                    'language': 'auto',
                    'task': 'transcribe'
                }
                
                print(f"📤 上传文件: {audio_file}")
                start_time = time.time()
                
                response = self.session.post(
                    f"{self.server_url}/transcribe",
                    files=files,
                    data=data,
                    timeout=60
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 识别成功:")
                    print(f"   文本: {result.get('text', 'N/A')}")
                    print(f"   语言: {result.get('language', 'N/A')}")
                    print(f"   处理时间: {processing_time:.2f}秒")
                    print(f"   服务器处理时间: {result.get('processing_time', 'N/A')}秒")
                    return True
                else:
                    print(f"❌ 识别失败: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   错误信息: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   响应内容: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
        finally:
            # 清理测试文件
            if audio_file and audio_file.startswith("test_") and os.path.exists(audio_file):
                try:
                    os.remove(audio_file)
                    print(f"🗑️  已清理测试文件: {audio_file}")
                except:
                    pass
    
    def test_performance(self, iterations=3):
        """性能测试"""
        print(f"\n⚡ 性能测试 ({iterations}次)...")
        
        audio_file = self.create_test_audio()
        if not audio_file:
            print("❌ 无法创建测试音频，跳过性能测试")
            return
        
        times = []
        
        try:
            for i in range(iterations):
                print(f"   第{i+1}次测试...")
                
                with open(audio_file, 'rb') as f:
                    files = {'audio': f}
                    data = {'language': 'auto'}
                    
                    start_time = time.time()
                    response = self.session.post(
                        f"{self.server_url}/transcribe",
                        files=files,
                        data=data,
                        timeout=60
                    )
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        processing_time = end_time - start_time
                        times.append(processing_time)
                        print(f"     ✅ 耗时: {processing_time:.2f}秒")
                    else:
                        print(f"     ❌ 失败: {response.status_code}")
            
            if times:
                avg_time = sum(times) / len(times)
                min_time = min(times)
                max_time = max(times)
                
                print(f"\n📊 性能统计:")
                print(f"   平均时间: {avg_time:.2f}秒")
                print(f"   最快时间: {min_time:.2f}秒")
                print(f"   最慢时间: {max_time:.2f}秒")
                print(f"   成功率: {len(times)}/{iterations} ({len(times)/iterations*100:.1f}%)")
            
        finally:
            # 清理测试文件
            if os.path.exists(audio_file):
                os.remove(audio_file)
    
    def run_all_tests(self):
        """运行所有测试"""
        print(f"🚀 开始测试Whisper服务: {self.server_url}")
        print("=" * 50)
        
        # 基础功能测试
        health_ok = self.test_health()
        if not health_ok:
            print("\n❌ 服务不可用，停止测试")
            return False
        
        info_ok = self.test_info()
        transcribe_ok = self.test_transcribe()
        
        # 性能测试
        if transcribe_ok:
            self.test_performance()
        
        print("\n" + "=" * 50)
        print("📋 测试总结:")
        print(f"   健康检查: {'✅' if health_ok else '❌'}")
        print(f"   服务信息: {'✅' if info_ok else '❌'}")
        print(f"   语音识别: {'✅' if transcribe_ok else '❌'}")
        
        all_passed = health_ok and info_ok and transcribe_ok
        print(f"\n🎯 总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
        
        return all_passed

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Whisper服务测试工具")
    parser.add_argument(
        "--url", 
        default="http://localhost:8000",
        help="服务器URL (默认: http://localhost:8000)"
    )
    parser.add_argument(
        "--audio",
        help="测试音频文件路径"
    )
    parser.add_argument(
        "--performance",
        action="store_true",
        help="只运行性能测试"
    )
    parser.add_argument(
        "--iterations",
        type=int,
        default=3,
        help="性能测试迭代次数 (默认: 3)"
    )
    
    args = parser.parse_args()
    
    tester = WhisperServerTester(args.url)
    
    if args.performance:
        tester.test_performance(args.iterations)
    elif args.audio:
        tester.test_transcribe(args.audio)
    else:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
